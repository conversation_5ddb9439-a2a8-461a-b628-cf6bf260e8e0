@extends('layouts.app')

@section('head')
<style>
.sortable-ghost {
    opacity: 0.4;
    background: #f3f4f6;
    border: 2px dashed #d1d5db;
}

.sortable-chosen {
    background: #eff6ff;
    border: 2px solid #3b82f6;
}

.sortable-drag {
    background: #ffffff;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: rotate(5deg);
}

.nested-sortable {
    min-height: 20px;
    transition: all 0.2s ease;
}

.nested-sortable:hover {
    background-color: #f9fafb;
    border-radius: 0.375rem;
}

.nested-sortable.hidden {
    display: none;
}

.menu-item:hover .nested-sortable.hidden {
    display: block;
}

.drop-zone-active {
    background-color: #dbeafe;
    border-color: #3b82f6;
}
</style>
@endsection

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Navigation Management</h1>
            <p class="mt-1 text-sm text-gray-600">Customize the sidebar navigation structure</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('dashboard') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back
            </a>
            <button onclick="showCreateModal()" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-150 ease-in-out">
                <i class="fas fa-plus mr-2"></i>
                Add Menu Item
            </button>
            <button onclick="resetNavigation()" class="inline-flex items-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white font-medium rounded-md transition duration-150 ease-in-out">
                <i class="fas fa-undo mr-2"></i>
                Reset to Default
            </button>
        </div>
    </div>



    <!-- Navigation Tree -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Current Navigation Structure</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Drag and drop to reorder menu items. Click "Add Sub-Item" to add children.</p>
        </div>
        <div class="border-t border-gray-200 dark:border-gray-700">
            <div class="px-4 py-5 sm:px-6">
                <div id="navigation-tree" class="space-y-2">
                    <!-- Navigation tree will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Modal -->
<div id="menuModal" class="fixed inset-0 bg-gray-600 dark:bg-gray-900 bg-opacity-50 dark:bg-opacity-75 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-6 border border-gray-200 dark:border-gray-700 max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mb-4">
            <h3 id="modalTitle" class="text-xl leading-6 font-medium text-gray-900 dark:text-white">Add Menu Item</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Create or edit a navigation menu item</p>
        </div>

        <form id="menuForm" class="space-y-6">
            <input type="hidden" id="menuId" name="id">

            <!-- Basic Information Section -->
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Basic Information</h4>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="menuLabel" class="block text-sm font-medium text-gray-700">Display Name *</label>
                        <input type="text" id="menuLabel" name="label" required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                               placeholder="e.g., Dashboard, Settings">
                        <p class="mt-1 text-xs text-gray-500">Text shown in the navigation menu</p>
                    </div>

                    <div>
                        <label for="menuName" class="block text-sm font-medium text-gray-700">System Name *</label>
                        <input type="text" id="menuName" name="name" required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                               placeholder="e.g., dashboard, settings">
                        <p class="mt-1 text-xs text-gray-500">Unique identifier (auto-generated from display name)</p>
                    </div>
                </div>
            </div>

            <!-- Menu Type Section -->
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Menu Type & Behavior</h4>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="menuType" class="block text-sm font-medium text-gray-700">Item Type *</label>
                        <select id="menuType" name="type" required onchange="toggleModalFormFields()"
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                            <option value="link">Menu Link</option>
                            <option value="separator">Separator Line</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Choose between a clickable link or visual separator</p>
                    </div>

                    <div id="modalBehaviorField">
                        <label for="menuBehavior" class="block text-sm font-medium text-gray-700">Link Behavior *</label>
                        <select id="menuBehavior" name="behavior" onchange="toggleModalBehaviorFields()"
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                            <option value="internal">Internal Link</option>
                            <option value="external">External Link</option>
                            <option value="parent">Parent (with sub-items)</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500">How this menu item should behave when clicked</p>
                    </div>
                </div>
            </div>

            <!-- Appearance Section -->
            <div id="modalAppearanceSection" class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Appearance & Navigation</h4>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div id="modalIconField">
                        <label for="menuIcon" class="block text-sm font-medium text-gray-700">Icon</label>
                        <div class="mt-1 flex">
                            <input type="text" id="menuIcon" name="icon"
                                   class="block w-full border-gray-300 rounded-l-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                                   placeholder="fas fa-home">
                            <button type="button" onclick="showIconSelector('menuIcon')"
                                    class="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 hover:bg-gray-100">
                                <i class="fas fa-icons"></i>
                            </button>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Choose an icon for this menu item</p>
                    </div>

                    <div>
                        <label for="menuParent" class="block text-sm font-medium text-gray-700">Parent Menu</label>
                        <select id="menuParent" name="parent_id"
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                            <option value="">Top Level</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Place this item under another menu item</p>
                    </div>
                </div>

                <div id="modalUrlField" class="mt-4">
                    <label for="menuUrl" class="block text-sm font-medium text-gray-700">URL</label>
                    <input type="text" id="menuUrl" name="url"
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                           placeholder="/custom-page">
                    <p class="mt-1 text-xs text-gray-500">The URL this menu item should navigate to</p>
                </div>

                <div id="modalRouteField" class="mt-4">
                    <label for="menuRoute" class="block text-sm font-medium text-gray-700">Laravel Route (Optional)</label>

                    <!-- Route Selection Method -->
                    <div class="mt-2 mb-3">
                        <div class="flex items-center space-x-4">
                            <label class="inline-flex items-center">
                                <input type="radio" name="routeMethod" value="select" class="form-radio text-primary-600" checked>
                                <span class="ml-2 text-sm text-gray-700">Select from plugin routes</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="routeMethod" value="manual" class="form-radio text-primary-600">
                                <span class="ml-2 text-sm text-gray-700">Enter manually</span>
                            </label>
                        </div>
                    </div>

                    <!-- Plugin and Route Selection -->
                    <div id="routeSelectContainer">
                        <!-- Plugin Selection -->
                        <div class="mb-3">
                            <label for="routePluginSelect" class="block text-sm font-medium text-gray-700 mb-1">Select Plugin</label>
                            <select id="routePluginSelect" name="route_plugin"
                                    class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                <option value="">-- Select a plugin --</option>
                                <option value="core">Core System</option>
                            </select>
                        </div>

                        <!-- Route Selection -->
                        <div>
                            <label for="menuRouteSelect" class="block text-sm font-medium text-gray-700 mb-1">Select Route</label>
                            <select id="menuRouteSelect" name="route_select"
                                    class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                                    disabled>
                                <option value="">-- Select a plugin first --</option>
                            </select>
                        </div>
                    </div>

                    <!-- Manual Route Input -->
                    <div id="routeManualContainer" style="display: none;">
                        <input type="text" id="menuRoute" name="route"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                               placeholder="dashboard">
                    </div>

                    <p class="mt-1 text-xs text-gray-500">Use Laravel route name instead of URL (takes priority over URL)</p>
                </div>
            </div>

            <!-- Advanced Settings Section -->
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Advanced Settings</h4>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="menuPlugin" class="block text-sm font-medium text-gray-700">Plugin</label>
                        <select id="menuPlugin" name="plugin"
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                            <option value="">No Plugin</option>
                            @foreach($availablePlugins as $pluginName => $plugin)
                                <option value="{{ $pluginName }}">{{ $plugin->config['display_name'] ?? ucfirst($pluginName) }}</option>
                            @endforeach
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Associate with a plugin (optional)</p>
                    </div>

                    <div>
                        <label for="menuTarget" class="block text-sm font-medium text-gray-700">Link Target</label>
                        <select id="menuTarget" name="target"
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                            <option value="_self">Same Window</option>
                            <option value="_blank">New Window</option>
                            <option value="_parent">Parent Frame</option>
                            <option value="_top">Top Frame</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Where to open the link</p>
                    </div>
                </div>

                <div class="mt-4">
                    <label for="menuPermissions" class="block text-sm font-medium text-gray-700">Required Permissions</label>
                    <div class="mt-1 relative">
                        <button type="button" id="permissionDropdownBtn" onclick="togglePermissionDropdown()"
                                class="w-full bg-white border border-gray-300 rounded-md shadow-sm px-3 py-2 text-left cursor-pointer focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            <span id="permissionDropdownText" class="block truncate text-gray-500">Select permissions...</span>
                            <span class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </span>
                        </button>

                        <div id="permissionDropdown" class="hidden absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
                            <div class="px-3 py-2 border-b border-gray-200">
                                <input type="text" id="permissionSearch" placeholder="Search permissions..."
                                       class="w-full border-gray-300 rounded-md text-sm focus:ring-primary-500 focus:border-primary-500"
                                       onkeyup="filterPermissions()">
                            </div>
                            @foreach($availablePermissions as $permission)
                                <label class="permission-option flex items-center px-3 py-2 hover:bg-gray-100 cursor-pointer" data-permission="{{ $permission->name }}">
                                    <input type="checkbox" value="{{ $permission->name }}"
                                           class="permission-checkbox rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                                           onchange="updatePermissionSelection()">
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">{{ $permission->display_name }}</div>
                                        @if($permission->description)
                                            <div class="text-xs text-gray-500">{{ $permission->description }}</div>
                                        @endif
                                        @if($permission->plugin)
                                            <div class="text-xs text-blue-600">Plugin: {{ $permission->plugin }}</div>
                                        @endif
                                    </div>
                                </label>
                            @endforeach
                        </div>
                    </div>
                    <input type="hidden" id="menuPermissions" name="permissions">
                    <p class="mt-1 text-xs text-gray-500">Select permissions needed to see this menu item</p>
                </div>

                <div class="mt-4 flex items-center space-x-6">
                    <label class="flex items-center">
                        <input type="checkbox" id="menuActive" name="is_active" checked
                               class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-900">Active</span>
                        <span class="ml-1 text-xs text-gray-500">(functional)</span>
                    </label>

                    <label class="flex items-center">
                        <input type="checkbox" id="menuVisible" name="visible" checked
                               class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-900">Visible</span>
                        <span class="ml-1 text-xs text-gray-500">(shown in menu)</span>
                    </label>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-600">
                <button type="button" onclick="hideMenuModal()"
                        class="px-6 py-2 bg-gray-500 text-white font-medium rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300 transition duration-150 ease-in-out">
                    Cancel
                </button>
                <button type="submit"
                        class="px-6 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-300 transition duration-150 ease-in-out">
                    <i class="fas fa-save mr-2"></i>
                    Save Menu Item
                </button>
            </div>
            </form>
        </div>
    </div>
</div>

<!-- Icon Selector Modal -->
<div id="iconSelectorModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-4/5 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Select an Icon</h3>
                <button onclick="hideIconSelector()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Search -->
            <div class="mb-4">
                <input type="text" id="iconSearch" placeholder="Search icons..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                       onkeyup="filterIcons()">
            </div>

            <!-- Icon Categories -->
            <div class="mb-4">
                <div class="flex flex-wrap gap-2">
                    <button onclick="filterByCategory('all')" class="icon-category-btn active px-3 py-1 text-sm bg-blue-500 text-white rounded">All</button>
                    <button onclick="filterByCategory('solid')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300">Solid</button>
                    <button onclick="filterByCategory('brands')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300">Brands</button>
                    <button onclick="filterByCategory('social')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300">Social</button>
                    <button onclick="filterByCategory('business')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300">Business</button>
                </div>
            </div>

            <!-- Icons Grid -->
            <div id="iconsGrid" class="grid grid-cols-8 md:grid-cols-12 lg:grid-cols-16 gap-2 max-h-96 overflow-y-auto">
                <!-- Icons will be populated by JavaScript -->
            </div>

            <div class="mt-4 flex justify-end">
                <button onclick="hideIconSelector()" class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium rounded-md">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
<script>
// Pass enabled plugins data to JavaScript
window.enabledPlugins = <?php echo json_encode(array_keys($availablePlugins ?? [])); ?>;
</script>
<script>
let navigationData = [];
let isEditing = false;
let editingId = null;
let currentTargetInput = null;
let isAutoGeneratingName = true;

document.addEventListener('DOMContentLoaded', function() {
    loadNavigationTree();

    // Auto-generate system name from display name
    const labelInput = document.getElementById('menuLabel');
    const nameInput = document.getElementById('menuName');

    if (labelInput && nameInput) {
        labelInput.addEventListener('input', function() {
            if (isAutoGeneratingName) {
                const systemName = this.value.toLowerCase()
                    .replace(/[^a-z0-9\s]/g, '')
                    .replace(/\s+/g, '_')
                    .replace(/_{2,}/g, '_')
                    .replace(/^_|_$/g, '');
                nameInput.value = systemName;
            }
        });

        nameInput.addEventListener('input', function() {
            // If user manually edits the name, stop auto-generation
            isAutoGeneratingName = false;
        });
    }

    // Handle route selection method toggle
    const routeMethodRadios = document.querySelectorAll('input[name="routeMethod"]');
    const routeSelectContainer = document.getElementById('routeSelectContainer');
    const routeManualContainer = document.getElementById('routeManualContainer');

    routeMethodRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'select') {
                routeSelectContainer.style.display = 'block';
                routeManualContainer.style.display = 'none';
            } else {
                routeSelectContainer.style.display = 'none';
                routeManualContainer.style.display = 'block';
            }
        });
    });

    // Handle plugin selection for routes
    const routePluginSelect = document.getElementById('routePluginSelect');
    const routeSelect = document.getElementById('menuRouteSelect');
    const routeInput = document.getElementById('menuRoute');

    if (routePluginSelect && routeSelect) {
        routePluginSelect.addEventListener('change', function() {
            loadRoutesForPlugin(this.value);
        });
    }

    // Handle route selection from dropdown
    if (routeSelect && routeInput) {
        routeSelect.addEventListener('change', function() {
            if (this.value) {
                routeInput.value = this.value;
            }
        });
    }

    // Load available plugins and routes
    loadAvailablePlugins();
});

function loadNavigationTree() {
    fetch('/navigation/tree')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                navigationData = data.tree;
                renderNavigationTree();
                initializeSortable();
            } else {
                showNotification('Failed to load navigation tree', 'error');
            }
        })
        .catch(error => {
            console.error('Error loading navigation tree:', error);
            showNotification('Error loading navigation tree', 'error');
        });
}

function loadAvailablePlugins() {
    // Get enabled plugins from the global variable
    const enabledPlugins = window.enabledPlugins || [];
    const routePluginSelect = document.getElementById('routePluginSelect');

    if (!routePluginSelect) return;

    // Clear existing options except the first one
    while (routePluginSelect.children.length > 1) {
        routePluginSelect.removeChild(routePluginSelect.lastChild);
    }

    // Add core system option
    const coreOption = document.createElement('option');
    coreOption.value = 'core';
    coreOption.textContent = 'Core System';
    routePluginSelect.appendChild(coreOption);

    // Add enabled plugins
    enabledPlugins.forEach(pluginName => {
        const option = document.createElement('option');
        option.value = pluginName;
        option.textContent = getPluginDisplayName(pluginName);
        routePluginSelect.appendChild(option);
    });
}

function getPluginDisplayName(pluginName) {
    const displayNames = {
        'users': 'User Management',
        'business': 'Business Management',
        'navigation': 'Navigation',
        'announcements': 'Announcements',
        'settings': 'Settings'
    };
    return displayNames[pluginName] || pluginName.charAt(0).toUpperCase() + pluginName.slice(1);
}

function loadRoutesForPlugin(pluginName) {
    const routeSelect = document.getElementById('menuRouteSelect');
    if (!routeSelect) return;

    // Clear existing routes
    routeSelect.innerHTML = '<option value="">-- Select a route --</option>';

    if (!pluginName) {
        routeSelect.disabled = true;
        return;
    }

    routeSelect.disabled = false;

    // Define routes for each plugin
    const pluginRoutes = {
        'core': [
            { value: 'dashboard', label: 'Dashboard - Main dashboard page' },
            { value: 'plugins.index', label: 'Plugin Manager - Plugin management interface' }
        ],
        'users': [
            { value: 'users.index', label: 'All Users - User listing page' },
            { value: 'users.create', label: 'Create User - Add new user form' },
            { value: 'users.show', label: 'View User - User details page' },
            { value: 'users.edit', label: 'Edit User - User edit form' },
            { value: 'roles.index', label: 'All Roles - Role management page' },
            { value: 'roles.create', label: 'Create Role - Add new role form' },
            { value: 'roles.show', label: 'View Role - Role details page' },
            { value: 'roles.edit', label: 'Edit Role - Role edit form' },
            { value: 'permissions.index', label: 'All Permissions - Permission management page' }
        ],
        'business': [
            { value: 'business.index', label: 'All Businesses - Business listing page' },
            { value: 'business.create', label: 'Create Business - Add new business form' },
            { value: 'business.show', label: 'View Business - Business details page' },
            { value: 'business.edit', label: 'Edit Business - Business edit form' },
            { value: 'business.contacts.index', label: 'All Contacts - Contact management page' },
            { value: 'business.contacts.create', label: 'Create Contact - Add new contact form' },
            { value: 'business.contacts.show', label: 'View Contact - Contact details page' },
            { value: 'business.contacts.edit', label: 'Edit Contact - Contact edit form' }
        ],
        'navigation': [
            { value: 'navigation.index', label: 'Navigation Manager - Navigation management interface' }
        ],
        'announcements': [
            { value: 'announcements.index', label: 'All Announcements - Announcement listing page' },
            { value: 'announcements.create', label: 'Create Announcement - Add new announcement form' },
            { value: 'announcements.show', label: 'View Announcement - Announcement details page' },
            { value: 'announcements.edit', label: 'Edit Announcement - Announcement edit form' }
        ],
        'settings': [
            { value: 'settings.index', label: 'System Settings - Main settings page' },
            { value: 'settings.general', label: 'General Settings - General configuration' },
            { value: 'settings.security', label: 'Security Settings - Security configuration' },
            { value: 'settings.backup', label: 'Backup Settings - Backup configuration' }
        ]
    };

    const routes = pluginRoutes[pluginName] || [];

    routes.forEach(route => {
        const option = document.createElement('option');
        option.value = route.value;
        option.textContent = route.label;
        routeSelect.appendChild(option);
    });
}

function findPluginForRoute(routeValue) {
    if (!routeValue) return null;

    // Define the same route mappings as in loadRoutesForPlugin
    const pluginRoutes = {
        'core': ['dashboard', 'plugins.index'],
        'users': ['users.index', 'users.create', 'users.show', 'users.edit', 'roles.index', 'roles.create', 'roles.show', 'roles.edit', 'permissions.index'],
        'business': ['business.index', 'business.create', 'business.show', 'business.edit', 'business.contacts.index', 'business.contacts.create', 'business.contacts.show', 'business.contacts.edit'],
        'navigation': ['navigation.index'],
        'announcements': ['announcements.index', 'announcements.create', 'announcements.show', 'announcements.edit'],
        'settings': ['settings.index', 'settings.general', 'settings.security', 'settings.backup']
    };

    // Find which plugin contains this route
    for (const [pluginName, routes] of Object.entries(pluginRoutes)) {
        if (routes.includes(routeValue)) {
            return pluginName;
        }
    }

    return null; // Route not found in predefined routes
}

function getSelectedRoute(formData) {
    // Check which route method is selected
    const routeMethodRadios = document.querySelectorAll('input[name="routeMethod"]');
    let selectedMethod = 'select'; // default

    routeMethodRadios.forEach(radio => {
        if (radio.checked) {
            selectedMethod = radio.value;
        }
    });

    if (selectedMethod === 'select') {
        // Get route from plugin/route dropdown selection
        const routeSelect = document.getElementById('menuRouteSelect');
        return routeSelect ? routeSelect.value : '';
    } else {
        // Get route from manual input
        return formData.get('route') || '';
    }
}

function renderNavigationTree() {
    const container = document.getElementById('navigation-tree');
    container.innerHTML = '';

    if (navigationData.length === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'p-4 text-gray-500 text-center';
        emptyMessage.textContent = 'No navigation items found. Use "Add Menu Item" to create new items or "Reset Navigation" to restore defaults.';
        container.appendChild(emptyMessage);
        return;
    }

    navigationData.forEach(item => {
        const element = createMenuElement(item);
        container.appendChild(element);
    });
}

function createMenuElement(item) {
    const div = document.createElement('div');
    div.className = 'menu-item bg-gray-50 border border-gray-200 rounded-lg p-3 mb-2';
    div.dataset.id = item.id;

    // Create the main content
    const mainContent = document.createElement('div');
    mainContent.className = 'flex items-center justify-between';

    // Left side content
    const leftContent = document.createElement('div');
    leftContent.className = 'flex items-center space-x-3';
    leftContent.innerHTML = `
        <i class="fas fa-grip-vertical text-gray-400 cursor-move"></i>
        <i class="${item.icon || 'fas fa-circle'} text-gray-600"></i>
        <div>
            <div class="font-medium text-gray-900">${item.label}</div>
            <div class="text-sm text-gray-500">${item.name}</div>
        </div>
        ${!item.is_active ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">Inactive</span>' : ''}
        ${item.is_system ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">System</span>' : ''}
    `;

    // Right side actions
    const rightContent = document.createElement('div');
    rightContent.className = 'flex items-center space-x-2';

    // Add Sub-Item button
    const addSubButton = document.createElement('button');
    addSubButton.className = 'text-green-600 hover:text-green-900 px-2 py-1 border border-green-300 rounded text-xs';
    addSubButton.title = 'Add Sub-Item';
    addSubButton.innerHTML = '<i class="fas fa-plus mr-1"></i>Sub-Item';
    addSubButton.onclick = () => showCreateModal(item.id);
    rightContent.appendChild(addSubButton);

    // Edit button
    const editButton = document.createElement('button');
    editButton.className = 'text-blue-600 hover:text-blue-900';
    editButton.title = 'Edit';
    editButton.innerHTML = '<i class="fas fa-edit"></i>';
    editButton.onclick = () => editMenuItem(item.id);
    rightContent.appendChild(editButton);

    // Delete button (only for non-system items)
    if (!item.is_system) {
        const deleteButton = document.createElement('button');
        deleteButton.className = 'text-red-600 hover:text-red-900';
        deleteButton.title = 'Delete';
        deleteButton.innerHTML = '<i class="fas fa-trash"></i>';
        deleteButton.onclick = () => deleteMenuItem(item.id);
        rightContent.appendChild(deleteButton);
    }

    mainContent.appendChild(leftContent);
    mainContent.appendChild(rightContent);
    div.appendChild(mainContent);

    // Add children if they exist
    if (item.filtered_children && item.filtered_children.length > 0) {
        const childrenContainer = document.createElement('div');
        childrenContainer.className = 'ml-6 mt-3 space-y-2 nested-sortable';
        childrenContainer.dataset.parentId = item.id;

        item.filtered_children.forEach(child => {
            const childElement = createMenuElement(child);
            childrenContainer.appendChild(childElement);
        });

        div.appendChild(childrenContainer);
    } else {
        // Add an empty drop zone for items without children
        const dropZone = document.createElement('div');
        dropZone.className = 'ml-6 mt-2 h-8 border-2 border-dashed border-gray-300 rounded nested-sortable hidden';
        dropZone.dataset.parentId = item.id;
        dropZone.innerHTML = '<div class="flex items-center justify-center h-full text-xs text-gray-400">Drop items here to make them sub-items</div>';
        div.appendChild(dropZone);
    }

    return div;
}

function initializeSortable() {
    const container = document.getElementById('navigation-tree');

    new Sortable(container, {
        animation: 150,
        handle: '.fa-grip-vertical',
        group: 'navigation',
        fallbackOnBody: true,
        swapThreshold: 0.65,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',
        onEnd: function(evt) {
            handleDragEnd(evt);
        },
        onMove: function(evt) {
            return handleDragMove(evt);
        },
        onAdd: function(evt) {
            // Show drop zone when dragging over
            const dropZone = evt.to;
            if (dropZone.classList.contains('nested-sortable')) {
                dropZone.classList.add('drop-zone-active');
            }
        },
        onRemove: function(evt) {
            // Hide drop zone when dragging away
            const dropZone = evt.from;
            if (dropZone.classList.contains('nested-sortable')) {
                dropZone.classList.remove('drop-zone-active');
            }
        },
        onAdd: function(evt) {
            // Show drop zone when dragging over
            const dropZone = evt.to;
            if (dropZone.classList.contains('nested-sortable')) {
                dropZone.classList.add('drop-zone-active');
            }
        },
        onRemove: function(evt) {
            // Hide drop zone when dragging away
            const dropZone = evt.from;
            if (dropZone.classList.contains('nested-sortable')) {
                dropZone.classList.remove('drop-zone-active');
            }
        }
    });

    // Initialize sortable for nested containers
    initializeNestedSortable();
}

function initializeNestedSortable() {
    const nestedContainers = document.querySelectorAll('.nested-sortable');

    nestedContainers.forEach(container => {
        new Sortable(container, {
            animation: 150,
            handle: '.fa-grip-vertical',
            group: 'navigation',
            fallbackOnBody: true,
            swapThreshold: 0.65,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            onEnd: function(evt) {
                handleDragEnd(evt);
            },
            onMove: function(evt) {
                return handleDragMove(evt);
            }
        });
    });
}

function handleDragMove(evt) {
    const draggedElement = evt.dragged;
    const relatedElement = evt.related;

    // Prevent dropping a parent into its own child
    if (isDescendant(relatedElement, draggedElement)) {
        return false;
    }

    return true;
}

function handleDragEnd(evt) {
    const draggedElement = evt.item;
    const targetContainer = evt.to;
    const sourceContainer = evt.from;

    const draggedId = parseInt(draggedElement.dataset.id);
    const targetParentElement = targetContainer.closest('.menu-item');
    const targetParentId = targetParentElement ? parseInt(targetParentElement.dataset.id) : null;

    // Check if we're dropping onto a different parent
    if (targetContainer !== sourceContainer || targetParentId !== null) {
        updateMenuParent(draggedId, targetParentId);
    } else {
        updateMenuOrder();
    }
}

function isDescendant(parent, child) {
    let node = child.parentNode;
    while (node != null) {
        if (node === parent) {
            return true;
        }
        node = node.parentNode;
    }
    return false;
}



function showCreateModal(parentId = null) {
    isEditing = false;
    editingId = null;
    isAutoGeneratingName = true; // Enable auto-generation for new items

    // Set title based on whether this is a sub-item or main item
    if (parentId) {
        document.getElementById('modalTitle').textContent = 'Add Sub-Menu Item';
    } else {
        document.getElementById('modalTitle').textContent = 'Add Menu Item';
    }

    document.getElementById('menuForm').reset();
    document.getElementById('menuId').value = '';

    // Reset route selection to default (select method)
    document.querySelector('input[name="routeMethod"][value="select"]').checked = true;
    document.getElementById('routeSelectContainer').style.display = 'block';
    document.getElementById('routeManualContainer').style.display = 'none';

    // Reset plugin and route dropdowns
    const routePluginSelect = document.getElementById('routePluginSelect');
    const routeSelect = document.getElementById('menuRouteSelect');

    if (routePluginSelect) {
        routePluginSelect.value = '';
    }
    if (routeSelect) {
        routeSelect.innerHTML = '<option value="">-- Select a plugin first --</option>';
        routeSelect.disabled = true;
    }
    document.getElementById('menuRoute').value = '';

    // Populate parent options and set the parent if provided
    populateParentOptions();
    if (parentId) {
        document.getElementById('menuParent').value = parentId;
    }

    toggleModalFormFields(); // Reset field visibility
    document.getElementById('menuModal').classList.remove('hidden');
}

function toggleModalFormFields() {
    const typeSelect = document.getElementById('menuType');
    const behaviorField = document.getElementById('modalBehaviorField');
    const iconField = document.getElementById('modalIconField');
    const urlField = document.getElementById('modalUrlField');
    const routeField = document.getElementById('modalRouteField');
    const labelField = document.getElementById('menuLabel');

    if (typeSelect.value === 'separator') {
        behaviorField.style.display = 'none';
        iconField.style.display = 'none';
        urlField.style.display = 'none';
        routeField.style.display = 'none';
        labelField.placeholder = 'Section label (optional for separators)';
        labelField.required = false;
    } else {
        behaviorField.style.display = 'block';
        iconField.style.display = 'block';
        routeField.style.display = 'block';
        labelField.placeholder = 'Display text for the menu item';
        labelField.required = true;
        toggleModalBehaviorFields();
    }
}

function toggleModalBehaviorFields() {
    const behaviorSelect = document.getElementById('menuBehavior');
    const urlField = document.getElementById('modalUrlField');
    const urlInput = document.getElementById('menuUrl');

    if (behaviorSelect.value === 'parent') {
        urlField.style.display = 'none';
        urlInput.required = false;
    } else {
        urlField.style.display = 'block';
        urlInput.required = false; // URL is optional if route is specified

        if (behaviorSelect.value === 'external') {
            urlInput.placeholder = 'https://example.com';
        } else {
            urlInput.placeholder = '/custom-page';
        }
    }
}

function editMenuItem(id) {
    isEditing = true;
    editingId = id;

    // Find the menu item in navigationData
    const item = findMenuItemById(id, navigationData);
    if (!item) return;

    document.getElementById('modalTitle').textContent = 'Edit Menu Item';
    document.getElementById('menuId').value = item.id;
    document.getElementById('menuName').value = item.name;
    document.getElementById('menuType').value = item.type || 'link';

    // Determine behavior based on existing data
    let behavior = 'internal';
    if (item.target === '_blank') {
        behavior = 'external';
    } else if (item.url === '#' || !item.url) {
        behavior = 'parent';
    }
    document.getElementById('menuBehavior').value = behavior;

    document.getElementById('menuLabel').value = item.label;
    document.getElementById('menuIcon').value = item.icon || '';
    document.getElementById('menuUrl').value = item.url || '';

    // Handle route selection
    const routeValue = item.route || '';
    document.getElementById('menuRoute').value = routeValue;

    // Try to find which plugin this route belongs to and set up the dropdowns
    const pluginForRoute = findPluginForRoute(routeValue);

    if (pluginForRoute && routeValue) {
        // Route exists in our predefined routes, use select method
        document.querySelector('input[name="routeMethod"][value="select"]').checked = true;
        document.getElementById('routeSelectContainer').style.display = 'block';
        document.getElementById('routeManualContainer').style.display = 'none';

        // Set the plugin dropdown
        const routePluginSelect = document.getElementById('routePluginSelect');
        routePluginSelect.value = pluginForRoute;

        // Load routes for the plugin and then set the route
        loadRoutesForPlugin(pluginForRoute);
        setTimeout(() => {
            const routeSelect = document.getElementById('menuRouteSelect');
            routeSelect.value = routeValue;
        }, 100);
    } else {
        // Route doesn't exist in our predefined routes or is empty, use manual method
        document.querySelector('input[name="routeMethod"][value="manual"]').checked = true;
        document.getElementById('routeSelectContainer').style.display = 'none';
        document.getElementById('routeManualContainer').style.display = 'block';
    }

    document.getElementById('menuPlugin').value = item.plugin || '';
    document.getElementById('menuTarget').value = item.target || '_self';
    document.getElementById('menuActive').checked = item.is_active;
    document.getElementById('menuVisible').checked = item.visible ?? true;

    // Set permissions using the permission selector
    setPermissionSelection(item.permissions || []);

    // Disable auto-generation for existing items
    isAutoGeneratingName = false;

    populateParentOptions(item.id);
    document.getElementById('menuParent').value = item.parent_id || '';

    // Toggle form fields based on type
    toggleModalFormFields();

    document.getElementById('menuModal').classList.remove('hidden');
}

function hideMenuModal() {
    document.getElementById('menuModal').classList.add('hidden');
}

function populateParentOptions(excludeId = null) {
    const select = document.getElementById('menuParent');
    select.innerHTML = '<option value="">Top Level</option>';

    function addOptions(items, level = 0) {
        items.forEach(item => {
            if (item.id !== excludeId) {
                const option = document.createElement('option');
                option.value = item.id;
                option.textContent = '  '.repeat(level) + item.label;
                select.appendChild(option);

                if (item.filtered_children && item.filtered_children.length > 0) {
                    addOptions(item.filtered_children, level + 1);
                }
            }
        });
    }

    addOptions(navigationData);
}

function findMenuItemById(id, items) {
    for (const item of items) {
        if (item.id === id) {
            return item;
        }
        if (item.filtered_children) {
            const found = findMenuItemById(id, item.filtered_children);
            if (found) return found;
        }
    }
    return null;
}

// Form submission
document.getElementById('menuForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const behavior = formData.get('behavior') || 'internal';
    let url = formData.get('url');
    let target = formData.get('target') || '_self';

    // Adjust URL and target based on behavior
    if (behavior === 'parent' && !url) {
        url = '#';
    } else if (behavior === 'external') {
        target = '_blank';
    }

    const data = {
        name: formData.get('name'),
        type: formData.get('type') || 'link',
        label: formData.get('label'),
        icon: formData.get('icon'),
        url: url,
        route: getSelectedRoute(formData),
        plugin: formData.get('plugin'),
        parent_id: formData.get('parent_id') || null,
        target: target,
        is_active: formData.has('is_active'),
        visible: formData.has('visible'),
        permissions: formData.get('permissions') ? formData.get('permissions').split(',').map(p => p.trim()).filter(p => p) : []
    };

    // Validate required fields
    if (!data.name) {
        showNotification('Name is required', 'error');
        return;
    }
    if (!data.label && data.type !== 'separator') {
        showNotification('Label is required for links', 'error');
        return;
    }

    const apiUrl = isEditing ? `/navigation/${editingId}` : '/navigation';
    const method = isEditing ? 'PUT' : 'POST';
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    console.log('Making request to:', apiUrl);
    console.log('Method:', method);
    console.log('CSRF Token:', csrfToken);
    console.log('Data:', JSON.stringify(data, null, 2));

    fetch(apiUrl, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken,
            'Accept': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        return response.text().then(text => {
            console.log('Raw response:', text);
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('JSON parse error:', e);
                console.error('Response text:', text);
                throw new Error('Invalid JSON response: ' + text.substring(0, 100));
            }
        });
    })
    .then(data => {
        if (data.success) {
            hideMenuModal();
            loadNavigationTree();
            showNotification(data.message, 'success');
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error saving menu item:', error);
        showNotification('Error saving menu item', 'error');
    });
});



function deleteMenuItem(id) {
    // Find the menu item in navigationData to check if it has children
    const item = findMenuItemById(id, navigationData);
    if (!item) {
        showNotification('Menu item not found', 'error');
        return;
    }

    // Create appropriate confirmation message
    let confirmMessage = `Are you sure you want to delete "${item.label}"?`;
    if (item.filtered_children && item.filtered_children.length > 0) {
        confirmMessage += `\n\nThis will also delete ${item.filtered_children.length} sub-menu item(s).`;
    }
    confirmMessage += '\n\nThis action cannot be undone.';

    if (!confirm(confirmMessage)) {
        return;
    }

    // Show loading state
    showNotification('Deleting menu item...', 'info');

    fetch(`/navigation/${id}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            loadNavigationTree();
            showNotification(data.message, 'success');
        } else {
            showNotification(data.message || 'Failed to delete menu item', 'error');
        }
    })
    .catch(error => {
        console.error('Error deleting menu item:', error);
        showNotification('Error deleting menu item: ' + error.message, 'error');
    });
}

function updateMenuOrder() {
    const items = [];

    // Process root level items
    const rootContainer = document.getElementById('navigation-tree');
    const rootItems = rootContainer.children;

    Array.from(rootItems).forEach((item, index) => {
        if (item.classList.contains('menu-item')) {
            const itemData = {
                id: parseInt(item.dataset.id),
                sort_order: index,
                parent_id: null
            };
            items.push(itemData);

            // Process children
            const childContainer = item.querySelector('.nested-sortable');
            if (childContainer) {
                const childItems = childContainer.children;
                Array.from(childItems).forEach((childItem, childIndex) => {
                    if (childItem.classList.contains('menu-item')) {
                        items.push({
                            id: parseInt(childItem.dataset.id),
                            sort_order: childIndex,
                            parent_id: parseInt(item.dataset.id)
                        });
                    }
                });
            }
        }
    });

    fetch('/navigation/update-order', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ items })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Menu order updated', 'success');
            loadNavigationTree(); // Refresh the tree
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error updating menu order:', error);
        showNotification('Error updating menu order', 'error');
    });
}

function updateMenuParent(itemId, newParentId) {
    fetch(`/navigation/${itemId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            parent_id: newParentId,
            _method: 'PUT'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Menu structure updated', 'success');
            loadNavigationTree(); // Refresh the tree
        } else {
            showNotification(data.message, 'error');
            loadNavigationTree(); // Refresh on error to revert changes
        }
    })
    .catch(error => {
        console.error('Error updating menu parent:', error);
        showNotification('Error updating menu structure', 'error');
        loadNavigationTree(); // Refresh on error to revert changes
    });
}

function resetNavigation() {
    if (!confirm('Are you sure you want to reset the navigation to default? This will remove all custom menu items.')) {
        return;
    }

    fetch('/navigation/reset', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadNavigationTree();
            showNotification(data.message, 'success');
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error resetting navigation:', error);
        showNotification('Error resetting navigation', 'error');
    });
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove after 5 seconds
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

// Permission Selector Functions
function togglePermissionDropdown() {
    const dropdown = document.getElementById('permissionDropdown');
    dropdown.classList.toggle('hidden');
}

function filterPermissions() {
    const searchTerm = document.getElementById('permissionSearch').value.toLowerCase();
    const options = document.querySelectorAll('.permission-option');

    options.forEach(option => {
        const text = option.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            option.style.display = 'flex';
        } else {
            option.style.display = 'none';
        }
    });
}

function updatePermissionSelection() {
    const checkboxes = document.querySelectorAll('.permission-checkbox:checked');
    const selectedPermissions = Array.from(checkboxes).map(cb => cb.value);
    const hiddenInput = document.getElementById('menuPermissions');
    const displayText = document.getElementById('permissionDropdownText');

    hiddenInput.value = selectedPermissions.join(',');

    if (selectedPermissions.length === 0) {
        displayText.textContent = 'Select permissions...';
        displayText.className = 'block truncate text-gray-500';
    } else if (selectedPermissions.length === 1) {
        const checkbox = document.querySelector(`.permission-checkbox[value="${selectedPermissions[0]}"]`);
        const label = checkbox.closest('.permission-option');
        const displayName = label.querySelector('.text-sm.font-medium').textContent;
        displayText.textContent = displayName;
        displayText.className = 'block truncate text-gray-900';
    } else {
        displayText.textContent = `${selectedPermissions.length} permissions selected`;
        displayText.className = 'block truncate text-gray-900';
    }
}

function setPermissionSelection(permissions) {
    // Clear all checkboxes first
    document.querySelectorAll('.permission-checkbox').forEach(cb => cb.checked = false);

    // Check the selected permissions
    if (permissions && permissions.length > 0) {
        permissions.forEach(permission => {
            const checkbox = document.querySelector(`.permission-checkbox[value="${permission}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    }

    updatePermissionSelection();
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const dropdown = document.getElementById('permissionDropdown');
    const button = document.getElementById('permissionDropdownBtn');

    if (dropdown && button && !dropdown.contains(event.target) && !button.contains(event.target)) {
        dropdown.classList.add('hidden');
    }
});

// Icon Selector Functions
let currentIconInputId = null;

const iconList = [
    // Common icons
    'fas fa-home', 'fas fa-user', 'fas fa-users', 'fas fa-cog', 'fas fa-settings', 'fas fa-dashboard', 'fas fa-tachometer-alt',
    'fas fa-chart-bar', 'fas fa-chart-line', 'fas fa-chart-pie', 'fas fa-file', 'fas fa-folder', 'fas fa-envelope',
    'fas fa-phone', 'fas fa-calendar', 'fas fa-clock', 'fas fa-bell', 'fas fa-star', 'fas fa-heart', 'fas fa-bookmark',
    'fas fa-tag', 'fas fa-tags', 'fas fa-search', 'fas fa-filter', 'fas fa-sort', 'fas fa-edit', 'fas fa-trash',
    'fas fa-plus', 'fas fa-minus', 'fas fa-times', 'fas fa-check', 'fas fa-arrow-left', 'fas fa-arrow-right',
    'fas fa-arrow-up', 'fas fa-arrow-down', 'fas fa-download', 'fas fa-upload', 'fas fa-save', 'fas fa-print',
    'fas fa-share', 'fas fa-link', 'fas fa-external-link-alt', 'fas fa-lock', 'fas fa-unlock', 'fas fa-key',
    'fas fa-shield-alt', 'fas fa-eye', 'fas fa-eye-slash', 'fas fa-info', 'fas fa-question', 'fas fa-exclamation',
    'fas fa-warning', 'fas fa-ban', 'fas fa-check-circle', 'fas fa-times-circle', 'fas fa-info-circle',
    'fas fa-question-circle', 'fas fa-exclamation-circle', 'fas fa-lightbulb', 'fas fa-magic', 'fas fa-wrench',
    'fas fa-tools', 'fas fa-hammer', 'fas fa-screwdriver', 'fas fa-paint-brush', 'fas fa-palette', 'fas fa-image',
    'fas fa-images', 'fas fa-camera', 'fas fa-video', 'fas fa-music', 'fas fa-headphones', 'fas fa-microphone',
    'fas fa-volume-up', 'fas fa-volume-down', 'fas fa-volume-mute', 'fas fa-play', 'fas fa-pause', 'fas fa-stop',
    'fas fa-forward', 'fas fa-backward', 'fas fa-step-forward', 'fas fa-step-backward', 'fas fa-eject',
    'fas fa-random', 'fas fa-repeat', 'fas fa-wifi', 'fas fa-signal', 'fas fa-battery-full', 'fas fa-battery-half',
    'fas fa-battery-empty', 'fas fa-plug', 'fas fa-power-off', 'fas fa-desktop', 'fas fa-laptop', 'fas fa-tablet',
    'fas fa-mobile', 'fas fa-keyboard', 'fas fa-mouse', 'fas fa-gamepad', 'fas fa-tv', 'fas fa-radio',

    // Business icons
    'fas fa-briefcase', 'fas fa-building', 'fas fa-industry', 'fas fa-store', 'fas fa-shopping-cart',
    'fas fa-shopping-bag', 'fas fa-credit-card', 'fas fa-money-bill', 'fas fa-coins', 'fas fa-dollar-sign',
    'fas fa-euro-sign', 'fas fa-pound-sign', 'fas fa-yen-sign', 'fas fa-calculator', 'fas fa-receipt',
    'fas fa-handshake', 'fas fa-signature', 'fas fa-chart-area', 'fas fa-chart-pie', 'fas fa-analytics',
    'fas fa-balance-scale', 'fas fa-gavel', 'fas fa-clipboard', 'fas fa-clipboard-list', 'fas fa-tasks',

    // Social Media & Communication icons
    'fab fa-whatsapp', 'fab fa-twitter', 'fab fa-facebook', 'fab fa-facebook-f', 'fab fa-instagram',
    'fab fa-linkedin', 'fab fa-linkedin-in', 'fab fa-youtube', 'fab fa-telegram', 'fab fa-snapchat',
    'fab fa-tiktok', 'fab fa-discord', 'fab fa-slack', 'fab fa-skype', 'fab fa-viber', 'fab fa-pinterest',
    'fab fa-reddit', 'fab fa-tumblr', 'fab fa-twitch', 'fab fa-github', 'fab fa-gitlab', 'fab fa-bitbucket',
    'fab fa-google', 'fab fa-google-plus', 'fab fa-apple', 'fab fa-microsoft', 'fab fa-amazon',
    'fas fa-envelope', 'fas fa-phone', 'fas fa-mobile-alt', 'fas fa-fax', 'fas fa-comments', 'fas fa-comment',
    'fas fa-sms', 'fas fa-at', 'fas fa-hashtag', 'fas fa-share-alt', 'fas fa-share-square',

    // E-commerce & Payment icons
    'fab fa-paypal', 'fab fa-stripe', 'fab fa-cc-visa', 'fab fa-cc-mastercard', 'fab fa-cc-amex',
    'fab fa-cc-discover', 'fab fa-bitcoin', 'fab fa-ethereum', 'fas fa-credit-card', 'fas fa-money-check',
    'fas fa-wallet', 'fas fa-cash-register', 'fas fa-receipt', 'fas fa-invoice', 'fas fa-percentage',

    // Technology & Development icons
    'fab fa-html5', 'fab fa-css3', 'fab fa-js', 'fab fa-react', 'fab fa-vue', 'fab fa-angular',
    'fab fa-node-js', 'fab fa-php', 'fab fa-python', 'fab fa-java', 'fab fa-swift', 'fab fa-android',
    'fab fa-docker', 'fab fa-aws', 'fab fa-digital-ocean', 'fas fa-server', 'fas fa-database',
    'fas fa-cloud', 'fas fa-code', 'fas fa-terminal', 'fas fa-bug', 'fas fa-cogs',

    // Transportation & Location icons
    'fas fa-car', 'fas fa-truck', 'fas fa-bus', 'fas fa-taxi', 'fas fa-plane', 'fas fa-ship',
    'fas fa-bicycle', 'fas fa-motorcycle', 'fas fa-train', 'fas fa-subway', 'fas fa-map',
    'fas fa-map-marker', 'fas fa-map-marker-alt', 'fas fa-location-arrow', 'fas fa-compass',
    'fas fa-route', 'fas fa-road', 'fas fa-parking', 'fas fa-gas-pump',

    // Food & Restaurant icons
    'fas fa-utensils', 'fas fa-coffee', 'fas fa-wine-glass', 'fas fa-beer', 'fas fa-cocktail',
    'fas fa-pizza-slice', 'fas fa-hamburger', 'fas fa-ice-cream', 'fas fa-birthday-cake',
    'fas fa-apple-alt', 'fas fa-carrot', 'fas fa-fish', 'fas fa-cheese',

    // Health & Medical icons
    'fas fa-hospital', 'fas fa-ambulance', 'fas fa-user-md', 'fas fa-stethoscope', 'fas fa-pills',
    'fas fa-syringe', 'fas fa-thermometer', 'fas fa-heartbeat', 'fas fa-dna', 'fas fa-tooth',
    'fas fa-eye', 'fas fa-brain', 'fas fa-hand-holding-heart', 'fas fa-first-aid',

    // Sports & Recreation icons
    'fas fa-football-ball', 'fas fa-basketball-ball', 'fas fa-baseball-ball', 'fas fa-tennis-ball',
    'fas fa-volleyball-ball', 'fas fa-golf-ball', 'fas fa-hockey-puck', 'fas fa-bowling-ball',
    'fas fa-running', 'fas fa-swimming-pool', 'fas fa-skiing', 'fas fa-hiking', 'fas fa-biking',
    'fas fa-dumbbell', 'fas fa-trophy', 'fas fa-medal', 'fas fa-award',

    // Weather & Nature icons
    'fas fa-sun', 'fas fa-moon', 'fas fa-cloud', 'fas fa-cloud-rain', 'fas fa-cloud-snow',
    'fas fa-bolt', 'fas fa-rainbow', 'fas fa-snowflake', 'fas fa-wind', 'fas fa-temperature-high',
    'fas fa-temperature-low', 'fas fa-tree', 'fas fa-leaf', 'fas fa-seedling', 'fas fa-flower',

    // Navigation icons
    'fas fa-bars', 'fas fa-list', 'fas fa-th', 'fas fa-th-large', 'fas fa-th-list', 'fas fa-grip-horizontal',
    'fas fa-grip-vertical', 'fas fa-ellipsis-h', 'fas fa-ellipsis-v', 'fas fa-chevron-left', 'fas fa-chevron-right',
    'fas fa-chevron-up', 'fas fa-chevron-down', 'fas fa-angle-left', 'fas fa-angle-right', 'fas fa-angle-up',
    'fas fa-angle-down', 'fas fa-caret-left', 'fas fa-caret-right', 'fas fa-caret-up', 'fas fa-caret-down'
];

function showIconSelector(inputId) {
    currentIconInputId = inputId;
    document.getElementById('iconSelectorModal').classList.remove('hidden');
    populateIcons();
}

function hideIconSelector() {
    document.getElementById('iconSelectorModal').classList.add('hidden');
    currentIconInputId = null;
}

function populateIcons(filteredIcons = null) {
    const iconsGrid = document.getElementById('iconsGrid');
    const icons = filteredIcons || iconList;

    iconsGrid.innerHTML = '';

    icons.forEach(iconClass => {
        const iconButton = document.createElement('button');
        iconButton.type = 'button';
        iconButton.className = 'icon-item p-3 border border-gray-200 rounded hover:bg-blue-50 hover:border-blue-300 transition duration-150 ease-in-out';
        iconButton.innerHTML = `<i class="${iconClass} text-lg"></i>`;
        iconButton.title = iconClass;
        iconButton.onclick = () => selectIcon(iconClass);

        iconsGrid.appendChild(iconButton);
    });
}

function selectIcon(iconClass) {
    if (currentIconInputId) {
        document.getElementById(currentIconInputId).value = iconClass;
        hideIconSelector();
    }
}

function filterIcons() {
    const searchTerm = document.getElementById('iconSearch').value.toLowerCase();
    const filteredIcons = iconList.filter(icon =>
        icon.toLowerCase().includes(searchTerm)
    );
    populateIcons(filteredIcons);
}

function filterByCategory(category) {
    // Update active button
    document.querySelectorAll('.icon-category-btn').forEach(btn => {
        btn.classList.remove('active', 'bg-blue-500', 'text-white');
        btn.classList.add('bg-gray-200', 'text-gray-700');
    });

    event.target.classList.add('active', 'bg-blue-500', 'text-white');
    event.target.classList.remove('bg-gray-200', 'text-gray-700');

    let filteredIcons;
    if (category === 'all') {
        filteredIcons = iconList;
    } else if (category === 'solid') {
        filteredIcons = iconList.filter(icon => icon.startsWith('fas '));
    } else if (category === 'regular') {
        filteredIcons = iconList.filter(icon => icon.startsWith('far '));
    } else if (category === 'brands') {
        filteredIcons = iconList.filter(icon => icon.startsWith('fab '));
    } else if (category === 'social') {
        // Social media and communication icons
        filteredIcons = iconList.filter(icon =>
            icon.includes('whatsapp') || icon.includes('twitter') || icon.includes('facebook') ||
            icon.includes('instagram') || icon.includes('linkedin') || icon.includes('youtube') ||
            icon.includes('telegram') || icon.includes('snapchat') || icon.includes('tiktok') ||
            icon.includes('discord') || icon.includes('slack') || icon.includes('skype') ||
            icon.includes('envelope') || icon.includes('phone') || icon.includes('comments')
        );
    } else if (category === 'business') {
        // Business and finance icons
        filteredIcons = iconList.filter(icon =>
            icon.includes('briefcase') || icon.includes('building') || icon.includes('store') ||
            icon.includes('money') || icon.includes('dollar') || icon.includes('credit-card') ||
            icon.includes('chart') || icon.includes('analytics') || icon.includes('handshake') ||
            icon.includes('calculator') || icon.includes('receipt') || icon.includes('invoice')
        );
    }

    populateIcons(filteredIcons);
}
</script>
@endsection
