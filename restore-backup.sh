#!/bin/bash

# Backup restoration script for SQLite database

echo "=== Backup Restoration Script ==="

# Paths
DB_PATH="database/database.sqlite"
BACKUP_PATH="storage/app/backups/backup-2025-08-01_17-43-42/database.sql"

# Check if backup file exists
if [ ! -f "$BACKUP_PATH" ]; then
    echo "❌ Error: Backup file not found at $BACKUP_PATH"
    exit 1
fi

echo "📁 Backup file found: $BACKUP_PATH"

# Remove existing database if it exists
if [ -f "$DB_PATH" ]; then
    echo "🗑️  Removing existing database..."
    rm "$DB_PATH"
fi

# Create new database from backup
echo "📦 Restoring database from backup..."
sqlite3 "$DB_PATH" < "$BACKUP_PATH"

# Check if restoration was successful
if [ $? -eq 0 ]; then
    echo "✅ Database restoration completed successfully!"
    
    # Verify the restoration
    echo "🔍 Verifying restoration..."
    
    # Check migrations table
    MIGRATION_COUNT=$(sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM migrations;")
    echo "   - Migrations: $MIGRATION_COUNT records"
    
    # Check users table
    USER_COUNT=$(sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM users;")
    echo "   - Users: $USER_COUNT records"
    
    # Check if businesses table exists
    if sqlite3 "$DB_PATH" "SELECT name FROM sqlite_master WHERE type='table' AND name='businesses';" | grep -q businesses; then
        BUSINESS_COUNT=$(sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM businesses;")
        echo "   - Businesses: $BUSINESS_COUNT records"
    fi
    
    echo ""
    echo "🎉 Backup restoration completed successfully!"
    echo "Your application should now work with the restored data."
    
else
    echo "❌ Error: Database restoration failed!"
    exit 1
fi
