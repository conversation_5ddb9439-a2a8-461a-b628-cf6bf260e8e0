<?php

/**
 * <PERSON><PERSON><PERSON> to fix backup restoration issues with SQLite migrations table
 * This script handles the UNIQUE constraint violation when restoring backups
 */

// Get the database path
$dbPath = __DIR__ . '/database/database.sqlite';
$backupPath = __DIR__ . '/storage/app/backups/backup-2025-08-01_17-43-42/database.sql';

echo "=== Backup Restoration Fix Script ===\n";
echo "Database: $dbPath\n";
echo "Backup: $backupPath\n\n";

if (!file_exists($backupPath)) {
    die("Error: Backup file not found at: $backupPath\n");
}

try {
    // Read the backup SQL file
    echo "Reading backup file...\n";
    $backupSql = file_get_contents($backupPath);
    
    if ($backupSql === false) {
        die("Error: Could not read backup file\n");
    }
    
    // Remove the existing database file
    if (file_exists($dbPath)) {
        echo "Removing existing database...\n";
        unlink($dbPath);
    }
    
    // Create new database file
    echo "Creating new database...\n";
    touch($dbPath);
    
    // Connect to SQLite database
    echo "Connecting to database...\n";
    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Enable foreign keys
    $pdo->exec('PRAGMA foreign_keys = ON');
    
    // Split the SQL into individual statements
    echo "Processing SQL statements...\n";
    $statements = explode(';', $backupSql);
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        
        // Skip empty statements and comments
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $successCount++;
        } catch (PDOException $e) {
            echo "Warning: Failed to execute statement: " . substr($statement, 0, 100) . "...\n";
            echo "Error: " . $e->getMessage() . "\n";
            $errorCount++;
        }
    }
    
    echo "\n=== Restoration Complete ===\n";
    echo "Successful statements: $successCount\n";
    echo "Failed statements: $errorCount\n";
    
    // Verify the restoration
    echo "\nVerifying restoration...\n";
    $result = $pdo->query("SELECT COUNT(*) as count FROM migrations");
    $migrationCount = $result->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Migrations table has $migrationCount records\n";
    
    $result = $pdo->query("SELECT COUNT(*) as count FROM users");
    $userCount = $result->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Users table has $userCount records\n";
    
    echo "\n✅ Backup restoration completed successfully!\n";
    echo "You can now use your application with the restored data.\n";
    
} catch (Exception $e) {
    echo "❌ Error during restoration: " . $e->getMessage() . "\n";
    exit(1);
}
