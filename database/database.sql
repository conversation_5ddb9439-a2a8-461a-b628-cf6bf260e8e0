-- SQLite Database Dump
-- Generated on: 2025-08-01 17:43:42

-- Table: migrations
CREATE TABLE "migrations" ("id" integer primary key autoincrement not null, "migration" varchar not null, "batch" integer not null);

-- Data for table: migrations
INSERT INTO "migrations" VALUES ('1', '0001_01_01_000000_create_users_table', '1');
INSERT INTO "migrations" VALUES ('2', '0001_01_01_000001_create_cache_table', '1');
INSERT INTO "migrations" VALUES ('3', '0001_01_01_000002_create_jobs_table', '1');
INSERT INTO "migrations" VALUES ('4', '2025_07_27_105007_create_roles_and_permissions_tables', '2');
INSERT INTO "migrations" VALUES ('5', '2025_07_27_135455_add_blog_permissions', '3');
INSERT INTO "migrations" VALUES ('6', '2024_01_01_000001_create_businesses_table', '4');
INSERT INTO "migrations" VALUES ('7', '2024_01_01_000002_create_business_users_table', '4');
INSERT INTO "migrations" VALUES ('8', '2024_01_01_000003_enhance_businesses_table', '5');
INSERT INTO "migrations" VALUES ('9', '2024_01_01_000004_create_business_contacts_table', '5');
INSERT INTO "migrations" VALUES ('10', '2024_01_01_000005_create_business_documents_table', '5');
INSERT INTO "migrations" VALUES ('11', '2024_01_01_000006_create_tags_table', '5');
INSERT INTO "migrations" VALUES ('12', '2024_01_01_000007_create_business_tags_table', '5');
INSERT INTO "migrations" VALUES ('13', '2024_01_01_000008_create_products_table', '5');
INSERT INTO "migrations" VALUES ('14', '2024_01_01_000009_create_business_products_table', '5');
INSERT INTO "migrations" VALUES ('15', '2024_01_01_000010_add_logo_to_businesses_table', '6');
INSERT INTO "migrations" VALUES ('16', '2024_01_01_000011_add_status_to_businesses_table', '6');
INSERT INTO "migrations" VALUES ('17', '2024_01_01_000012_simplify_products_table', '7');
INSERT INTO "migrations" VALUES ('18', '2024_01_01_000013_create_business_activities_table', '8');
INSERT INTO "migrations" VALUES ('19', '2024_01_01_000014_create_business_comments_table', '8');
INSERT INTO "migrations" VALUES ('20', '2024_01_01_000015_create_business_notification_preferences_table', '8');
INSERT INTO "migrations" VALUES ('21', '2024_01_01_000016_create_business_activity_attachments_table', '8');
INSERT INTO "migrations" VALUES ('22', '2024_01_15_000006_add_whatsapp_fields_to_businesses_table', '9');
INSERT INTO "migrations" VALUES ('23', '2024_12_01_000001_add_whatsapp_fields_to_businesses_table', '10');
INSERT INTO "migrations" VALUES ('24', '2024_12_01_000002_add_taqnyat_fields_to_businesses_table', '11');
INSERT INTO "migrations" VALUES ('25', '2024_12_01_000004_add_churned_at_to_businesses_table', '12');
INSERT INTO "migrations" VALUES ('26', '2024_12_01_000005_add_lost_status_to_businesses_table', '13');
INSERT INTO "migrations" VALUES ('27', '2024_12_01_000006_update_status_constraint_for_lost', '14');
INSERT INTO "migrations" VALUES ('28', '2024_12_01_000007_add_billing_document_types', '15');
INSERT INTO "migrations" VALUES ('29', '2024_12_01_000008_add_chat_fields_to_business_activities', '15');
INSERT INTO "migrations" VALUES ('35', '2024_01_01_000001_create_products_table', '16');
INSERT INTO "migrations" VALUES ('36', '2024_01_01_000002_create_product_documents_table', '16');
INSERT INTO "migrations" VALUES ('37', '2024_01_01_000003_create_product_releases_table', '16');
INSERT INTO "migrations" VALUES ('38', '2024_01_01_000004_create_product_pricing_items_table', '16');
INSERT INTO "migrations" VALUES ('39', '2024_01_01_000005_update_business_products_integration', '16');
INSERT INTO "migrations" VALUES ('40', '2025_07_29_111341_enhance_product_pricing_items_table', '17');
INSERT INTO "migrations" VALUES ('41', '2025_07_29_111551_add_terms_to_products_table', '18');
INSERT INTO "migrations" VALUES ('42', '2025_07_29_145900_add_per_consumption_billing_cycle', '19');
INSERT INTO "migrations" VALUES ('43', '2024_01_01_000001_create_announcements_table', '20');
INSERT INTO "migrations" VALUES ('44', '2024_01_01_000002_create_announcement_reads_table', '20');
INSERT INTO "migrations" VALUES ('45', '2025_07_30_000001_create_languages_table', '21');
INSERT INTO "migrations" VALUES ('60', '2024_07_31_000000_create_taqnyat_settings_table', '22');
INSERT INTO "migrations" VALUES ('61', '2024_07_30_230000_add_admin_plugin_permissions', '23');
INSERT INTO "migrations" VALUES ('62', '2024_01_15_000001_add_plugin_to_permissions_table', '24');
INSERT INTO "migrations" VALUES ('63', '2025_07_30_000001_create_user_preferences_table', '24');
INSERT INTO "migrations" VALUES ('64', '2024_01_16_000001_create_navigation_menus_table', '25');

-- Table: password_reset_tokens
CREATE TABLE "password_reset_tokens" ("email" varchar not null, "token" varchar not null, "created_at" datetime, primary key ("email"));

-- Table: sessions
CREATE TABLE "sessions" ("id" varchar not null, "user_id" integer, "ip_address" varchar, "user_agent" text, "payload" text not null, "last_activity" integer not null, primary key ("id"));

-- Data for table: sessions
INSERT INTO "sessions" VALUES ('XproOKLRAgaCi0hUPO1r7sk82hCD3FBrWr1WPvzL', '1', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiY0FDb2tXdzFNNlNKclNXRklRTnVSTk9zMEUzR2RUa2FvNHFGM1loUSI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDU6Imh0dHA6Ly9idXNpbmVzcy50ZXN0L2Fubm91bmNlbWVudHMvYXBpL3VucmVhZCI7fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7fQ==', '1754070211');

-- Table: cache
CREATE TABLE "cache" ("key" varchar not null, "value" text not null, "expiration" integer not null, primary key ("key"));

-- Table: cache_locks
CREATE TABLE "cache_locks" ("key" varchar not null, "owner" varchar not null, "expiration" integer not null, primary key ("key"));

-- Table: jobs
CREATE TABLE "jobs" ("id" integer primary key autoincrement not null, "queue" varchar not null, "payload" text not null, "attempts" integer not null, "reserved_at" integer, "available_at" integer not null, "created_at" integer not null);

-- Table: job_batches
CREATE TABLE "job_batches" ("id" varchar not null, "name" varchar not null, "total_jobs" integer not null, "pending_jobs" integer not null, "failed_jobs" integer not null, "failed_job_ids" text not null, "options" text, "cancelled_at" integer, "created_at" integer not null, "finished_at" integer, primary key ("id"));

-- Table: failed_jobs
CREATE TABLE "failed_jobs" ("id" integer primary key autoincrement not null, "uuid" varchar not null, "connection" text not null, "queue" text not null, "payload" text not null, "exception" text not null, "failed_at" datetime not null default CURRENT_TIMESTAMP);

-- Table: roles
CREATE TABLE "roles" ("id" integer primary key autoincrement not null, "name" varchar not null, "display_name" varchar not null, "description" text, "created_at" datetime, "updated_at" datetime);

-- Data for table: roles
INSERT INTO "roles" VALUES ('1', 'admin', 'Administrator', 'Full system access with all permissions', '2025-07-27 10:52:07', '2025-07-27 10:52:07');
INSERT INTO "roles" VALUES ('2', 'editor', 'Editor', 'Can manage content and view dashboard', '2025-07-27 10:52:07', '2025-07-27 10:52:07');
INSERT INTO "roles" VALUES ('3', 'user', 'User', 'Basic user with limited access', '2025-07-27 10:52:07', '2025-07-27 10:52:07');

-- Table: permissions
CREATE TABLE "permissions" ("id" integer primary key autoincrement not null, "name" varchar not null, "display_name" varchar not null, "description" text, "created_at" datetime, "updated_at" datetime, "plugin" varchar);

-- Data for table: permissions
INSERT INTO "permissions" VALUES ('1', 'manage_users', 'Manage Users', 'Create, edit, and delete users', '2025-07-27 10:52:07', '2025-08-01 15:28:38', 'users');
INSERT INTO "permissions" VALUES ('2', 'manage_roles', 'Manage Roles', 'Create, edit, and delete roles', '2025-07-27 10:52:07', '2025-08-01 15:28:38', 'users');
INSERT INTO "permissions" VALUES ('3', 'manage_permissions', 'Manage Permissions', 'Assign and revoke permissions', '2025-07-27 10:52:07', '2025-08-01 15:28:38', 'users');
INSERT INTO "permissions" VALUES ('4', 'manage_plugins', 'Manage Plugins', 'Enable, disable, and configure plugins', '2025-07-27 10:52:07', '2025-07-27 10:52:07', NULL);
INSERT INTO "permissions" VALUES ('5', 'view_dashboard', 'View Dashboard', 'Access the main dashboard', '2025-07-27 10:52:07', '2025-07-27 10:52:07', NULL);
INSERT INTO "permissions" VALUES ('6', 'manage_blog', 'Manage Blog', 'Create, edit, and delete blog posts', '2025-07-27 10:52:07', '2025-07-27 10:52:07', NULL);
INSERT INTO "permissions" VALUES ('7', 'view_blog', 'View Blog', 'View blog posts', '2025-07-27 10:52:07', '2025-07-27 10:52:07', NULL);
INSERT INTO "permissions" VALUES ('8', 'create_posts', 'Create Posts', 'Create new blog posts', '2025-07-27 13:55:20', '2025-07-27 13:55:20', NULL);
INSERT INTO "permissions" VALUES ('9', 'edit_posts', 'Edit Posts', 'Edit existing blog posts', '2025-07-27 13:55:20', '2025-07-27 13:55:20', NULL);
INSERT INTO "permissions" VALUES ('10', 'delete_posts', 'Delete Posts', 'Delete blog posts', '2025-07-27 13:55:20', '2025-07-27 13:55:20', NULL);
INSERT INTO "permissions" VALUES ('11', 'manage_businesses', 'Manage Businesses', 'Create, edit, and delete business entities', '2025-07-27 18:58:53', '2025-08-01 15:28:38', 'business');
INSERT INTO "permissions" VALUES ('12', 'view_businesses', 'View Businesses', 'Read-only access to business data', '2025-07-27 18:58:53', '2025-08-01 15:28:38', 'business');
INSERT INTO "permissions" VALUES ('13', 'manage_business_users', 'Manage Business Users', 'Assign users to businesses and manage their roles', '2025-07-27 18:58:53', '2025-07-27 18:58:53', NULL);
INSERT INTO "permissions" VALUES ('14', 'view_business_reports', 'View Business Reports', 'Access business analytics and reports', '2025-07-27 18:58:53', '2025-07-27 18:58:53', NULL);
INSERT INTO "permissions" VALUES ('15', 'manage_products', 'Manage Products', 'Create, edit, and delete product entities', '2025-07-29 09:26:59', '2025-07-29 09:26:59', NULL);
INSERT INTO "permissions" VALUES ('16', 'view_products', 'View Products', 'Read-only access to product data', '2025-07-29 09:26:59', '2025-07-29 09:26:59', NULL);
INSERT INTO "permissions" VALUES ('17', 'manage_product_documents', 'Manage Product Documents', 'Upload, edit, and manage product documents', '2025-07-29 09:26:59', '2025-07-29 09:26:59', NULL);
INSERT INTO "permissions" VALUES ('18', 'view_product_documents', 'View Product Documents', 'Read-only access to product documents', '2025-07-29 09:26:59', '2025-07-29 09:26:59', NULL);
INSERT INTO "permissions" VALUES ('19', 'manage_product_releases', 'Manage Product Releases', 'Create and manage product releases and updates', '2025-07-29 09:26:59', '2025-07-29 09:26:59', NULL);
INSERT INTO "permissions" VALUES ('20', 'view_product_releases', 'View Product Releases', 'Read-only access to product release information', '2025-07-29 09:26:59', '2025-07-29 09:26:59', NULL);
INSERT INTO "permissions" VALUES ('21', 'manage_announcements', 'Manage Announcements', 'Create, edit, delete, and manage all announcements', '2025-07-30 11:05:02', '2025-08-01 15:28:38', 'announcements');
INSERT INTO "permissions" VALUES ('22', 'view_announcements', 'View Announcements', 'View and read announcements', '2025-07-30 11:05:02', '2025-08-01 15:28:38', 'announcements');
INSERT INTO "permissions" VALUES ('23', 'view_admin', 'View Admin', 'Can view admin dashboard and basic information', '2025-07-30 23:14:11', '2025-07-30 23:14:11', NULL);
INSERT INTO "permissions" VALUES ('24', 'manage_admin', 'Manage Admin', 'Can manage admin settings and configurations', '2025-07-30 23:14:11', '2025-07-30 23:14:11', NULL);
INSERT INTO "permissions" VALUES ('25', 'view_crm', 'View CRM', 'Can view CRM leads and prospects', '2025-07-30 23:14:11', '2025-07-30 23:14:11', NULL);
INSERT INTO "permissions" VALUES ('26', 'manage_crm', 'Manage CRM', 'Can create, edit, and manage CRM leads', '2025-07-30 23:14:11', '2025-07-30 23:14:11', NULL);
INSERT INTO "permissions" VALUES ('27', 'view_clients', 'View Clients', 'Can view client information and data', '2025-07-30 23:14:11', '2025-07-30 23:14:11', NULL);
INSERT INTO "permissions" VALUES ('28', 'manage_clients', 'Manage Clients', 'Can create, edit, and manage clients', '2025-07-30 23:14:11', '2025-07-30 23:14:11', NULL);
INSERT INTO "permissions" VALUES ('29', 'view_invoices', 'View Invoices', 'Can view invoices and billing information', '2025-07-30 23:14:11', '2025-07-30 23:14:11', NULL);
INSERT INTO "permissions" VALUES ('30', 'manage_invoices', 'Manage Invoices', 'Can create, edit, and manage invoices', '2025-07-30 23:14:11', '2025-07-30 23:14:11', NULL);
INSERT INTO "permissions" VALUES ('31', 'view_business_info', 'View Business Info', 'Can view business information and settings', '2025-07-30 23:14:11', '2025-07-30 23:14:11', NULL);
INSERT INTO "permissions" VALUES ('32', 'manage_business_info', 'Manage Business Info', 'Can edit and manage business information', '2025-07-30 23:14:11', '2025-07-30 23:14:11', NULL);
INSERT INTO "permissions" VALUES ('33', 'manage_settings', 'Manage Settings', 'Access to system settings, backups, and updates', '2025-08-01 15:10:47', '2025-08-01 15:28:38', 'settings');

-- Table: role_user
CREATE TABLE "role_user" ("id" integer primary key autoincrement not null, "user_id" integer not null, "role_id" integer not null, "created_at" datetime, "updated_at" datetime, foreign key("user_id") references "users"("id") on delete cascade, foreign key("role_id") references "roles"("id") on delete cascade);

-- Table: permission_role
CREATE TABLE "permission_role" ("id" integer primary key autoincrement not null, "permission_id" integer not null, "role_id" integer not null, "created_at" datetime, "updated_at" datetime, foreign key("permission_id") references "permissions"("id") on delete cascade, foreign key("role_id") references "roles"("id") on delete cascade);

-- Data for table: permission_role
INSERT INTO "permission_role" VALUES ('1', '1', '1', '2025-07-27 10:52:07', '2025-07-27 10:52:07');
INSERT INTO "permission_role" VALUES ('2', '2', '1', '2025-07-27 10:52:07', '2025-07-27 10:52:07');
INSERT INTO "permission_role" VALUES ('3', '3', '1', '2025-07-27 10:52:07', '2025-07-27 10:52:07');
INSERT INTO "permission_role" VALUES ('4', '4', '1', '2025-07-27 10:52:07', '2025-07-27 10:52:07');
INSERT INTO "permission_role" VALUES ('5', '5', '1', '2025-07-27 10:52:07', '2025-07-27 10:52:07');
INSERT INTO "permission_role" VALUES ('6', '6', '1', '2025-07-27 10:52:07', '2025-07-27 10:52:07');
INSERT INTO "permission_role" VALUES ('7', '7', '1', '2025-07-27 10:52:07', '2025-07-27 10:52:07');
INSERT INTO "permission_role" VALUES ('11', '7', '3', '2025-07-27 10:52:07', '2025-07-27 10:52:07');
INSERT INTO "permission_role" VALUES ('12', '5', '3', '2025-07-27 10:52:07', '2025-07-27 10:52:07');
INSERT INTO "permission_role" VALUES ('13', '8', '1', '2025-07-27 13:55:40', '2025-07-27 13:55:40');
INSERT INTO "permission_role" VALUES ('14', '9', '1', '2025-07-27 13:55:40', '2025-07-27 13:55:40');
INSERT INTO "permission_role" VALUES ('15', '10', '1', '2025-07-27 13:55:40', '2025-07-27 13:55:40');
INSERT INTO "permission_role" VALUES ('16', '13', '1', '2025-07-27 18:58:53', '2025-07-27 18:58:53');
INSERT INTO "permission_role" VALUES ('17', '11', '1', '2025-07-27 18:58:53', '2025-07-27 18:58:53');
INSERT INTO "permission_role" VALUES ('18', '14', '1', '2025-07-27 18:58:53', '2025-07-27 18:58:53');
INSERT INTO "permission_role" VALUES ('19', '12', '1', '2025-07-27 18:58:53', '2025-07-27 18:58:53');
INSERT INTO "permission_role" VALUES ('20', '14', '2', '2025-07-27 18:58:53', '2025-07-27 18:58:53');
INSERT INTO "permission_role" VALUES ('21', '12', '2', '2025-07-27 18:58:53', '2025-07-27 18:58:53');
INSERT INTO "permission_role" VALUES ('23', '15', '1', '2025-07-29 09:26:59', '2025-07-29 09:26:59');
INSERT INTO "permission_role" VALUES ('24', '16', '1', '2025-07-29 09:26:59', '2025-07-29 09:26:59');
INSERT INTO "permission_role" VALUES ('25', '17', '1', '2025-07-29 09:26:59', '2025-07-29 09:26:59');
INSERT INTO "permission_role" VALUES ('26', '18', '1', '2025-07-29 09:26:59', '2025-07-29 09:26:59');
INSERT INTO "permission_role" VALUES ('27', '19', '1', '2025-07-29 09:26:59', '2025-07-29 09:26:59');
INSERT INTO "permission_role" VALUES ('28', '20', '1', '2025-07-29 09:26:59', '2025-07-29 09:26:59');
INSERT INTO "permission_role" VALUES ('29', '16', '2', '2025-07-29 14:32:24', '2025-07-29 14:32:24');
INSERT INTO "permission_role" VALUES ('30', '17', '2', '2025-07-29 14:32:24', '2025-07-29 14:32:24');
INSERT INTO "permission_role" VALUES ('31', '18', '2', '2025-07-29 14:32:24', '2025-07-29 14:32:24');
INSERT INTO "permission_role" VALUES ('32', '20', '2', '2025-07-29 14:32:24', '2025-07-29 14:32:24');
INSERT INTO "permission_role" VALUES ('33', '21', '1', '2025-07-30 11:05:02', '2025-07-30 11:05:02');
INSERT INTO "permission_role" VALUES ('34', '22', '1', '2025-07-30 11:05:02', '2025-07-30 11:05:02');
INSERT INTO "permission_role" VALUES ('35', '22', '3', '2025-07-30 11:05:02', '2025-07-30 11:05:02');
INSERT INTO "permission_role" VALUES ('36', '22', '2', '2025-07-30 11:17:54', '2025-07-30 11:17:54');
INSERT INTO "permission_role" VALUES ('37', '24', '1', '2025-07-30 23:15:15', '2025-07-30 23:15:15');
INSERT INTO "permission_role" VALUES ('38', '32', '1', '2025-07-30 23:15:15', '2025-07-30 23:15:15');
INSERT INTO "permission_role" VALUES ('39', '28', '1', '2025-07-30 23:15:15', '2025-07-30 23:15:15');
INSERT INTO "permission_role" VALUES ('40', '26', '1', '2025-07-30 23:15:15', '2025-07-30 23:15:15');
INSERT INTO "permission_role" VALUES ('41', '30', '1', '2025-07-30 23:15:15', '2025-07-30 23:15:15');
INSERT INTO "permission_role" VALUES ('42', '23', '1', '2025-07-30 23:15:15', '2025-07-30 23:15:15');
INSERT INTO "permission_role" VALUES ('43', '31', '1', '2025-07-30 23:15:15', '2025-07-30 23:15:15');
INSERT INTO "permission_role" VALUES ('44', '27', '1', '2025-07-30 23:15:15', '2025-07-30 23:15:15');
INSERT INTO "permission_role" VALUES ('45', '25', '1', '2025-07-30 23:15:15', '2025-07-30 23:15:15');
INSERT INTO "permission_role" VALUES ('46', '29', '1', '2025-07-30 23:15:15', '2025-07-30 23:15:15');
INSERT INTO "permission_role" VALUES ('47', '23', '2', '2025-07-31 14:27:22', '2025-07-31 14:27:22');
INSERT INTO "permission_role" VALUES ('48', '33', '1', '2025-08-01 15:10:47', '2025-08-01 15:10:47');

-- Table: users
CREATE TABLE "users" ("id" integer primary key autoincrement not null, "name" varchar not null, "email" varchar not null, "email_verified_at" datetime, "password" varchar not null, "remember_token" varchar, "created_at" datetime, "updated_at" datetime, "role_id" integer, "is_active" tinyint(1) not null default '1', foreign key("role_id") references "roles"("id") on delete set null);

-- Data for table: users
INSERT INTO "users" VALUES ('1', 'Administrator', '<EMAIL>', '2025-07-27 10:52:07', '$2y$12$Bl8g4yZ8ob4YAnbP1rTJ.O.s943P/7LufWlZ220I0itAWWYX4tyMu', 'k1nB1rLVktkrDEAfi5j74qxAGA6mkrj06weJoVVGRqdlpiwW5jlaa7BcoWsH', '2025-07-27 10:52:07', '2025-07-27 10:52:07', '1', '1');
INSERT INTO "users" VALUES ('2', 'Editor User', '<EMAIL>', '2025-07-27 10:52:07', '$2y$12$kyM3.6nPAC2dsOCfkaO7k.Gv83mf.p8urXK6rZIwv.3qAi/oRhxZK', NULL, '2025-07-27 10:52:07', '2025-07-27 18:52:12', '2', '1');
INSERT INTO "users" VALUES ('3', 'Regular User', '<EMAIL>', '2025-07-27 10:52:08', '$2y$12$XcB9QagSUYBYDT15p3mCCOzTd5z3DsSL1TBAJzTHrDiktZA953Nja', NULL, '2025-07-27 10:52:08', '2025-07-27 18:52:40', '3', '1');

-- Table: businesses
CREATE TABLE "businesses" ("id" integer primary key autoincrement not null, "name" varchar not null, "description" text, "email" varchar, "phone" varchar, "address" text, "city" varchar, "state" varchar, "country" varchar, "postal_code" varchar, "website" varchar, "tax_id" varchar, "is_active" tinyint(1) not null default '1', "created_by" integer not null, "created_at" datetime, "updated_at" datetime, "deleted_at" datetime, "brand_name" varchar, "legal_name" varchar, "website_url" varchar, "primary_phone" varchar, "logo_url" varchar, "status" varchar check ("status" in ('lead', 'deal', 'customer', 'partner', 'churned')) not null default 'lead', "churn_reason" text, "whatsapp_enabled" tinyint(1) not null default '0', "meta_business_id" varchar, "whatsapp_id" varchar, "whatsapp_provider" varchar check ("whatsapp_provider" in ('taqnyat', '360dialog')), "message_quality" varchar, "messaging_tier" varchar, "whatsapp_settings" text, "whatsapp_verified_at" datetime, "meta_business_verified" tinyint(1) not null default '0', "whatsapp_business_verified" tinyint(1) not null default '0', "taqnyat_id" varchar, "taqnyat_username" varchar, "churned_at" datetime, "lost_reason" text, "lost_at" datetime, foreign key("created_by") references "users"("id") on delete cascade);

-- Table: business_users
CREATE TABLE "business_users" ("id" integer primary key autoincrement not null, "business_id" integer not null, "user_id" integer not null, "role" varchar not null default 'member', "is_active" tinyint(1) not null default '1', "created_at" datetime, "updated_at" datetime, foreign key("business_id") references "businesses"("id") on delete cascade, foreign key("user_id") references "users"("id") on delete cascade);

-- Table: business_contacts
CREATE TABLE "business_contacts" ("id" integer primary key autoincrement not null, "business_id" integer not null, "name" varchar not null, "position" varchar, "department" varchar, "email" varchar, "phone" varchar, "is_primary" tinyint(1) not null default '0', "notes" text, "created_at" datetime, "updated_at" datetime, foreign key("business_id") references "businesses"("id") on delete cascade);

-- Table: business_documents
CREATE TABLE "business_documents" ("id" integer primary key autoincrement not null, "business_id" integer not null, "document_type" varchar check ("document_type" in ('commercial_registration', 'tax_certificate', 'address_proof', 'license', 'contract', 'other')) not null, "file_name" varchar not null, "original_name" varchar not null, "file_path" varchar not null, "mime_type" varchar not null, "file_size" integer not null, "description" text, "uploaded_by" integer not null, "upload_date" datetime not null, "created_at" datetime, "updated_at" datetime, foreign key("business_id") references "businesses"("id") on delete cascade, foreign key("uploaded_by") references "users"("id") on delete cascade);

-- Table: tags
CREATE TABLE "tags" ("id" integer primary key autoincrement not null, "name" varchar not null, "slug" varchar not null, "color" varchar not null default '#3b82f6', "description" text, "is_active" tinyint(1) not null default '1', "created_by" integer not null, "created_at" datetime, "updated_at" datetime, foreign key("created_by") references "users"("id") on delete cascade);

-- Table: business_tags
CREATE TABLE "business_tags" ("id" integer primary key autoincrement not null, "business_id" integer not null, "tag_id" integer not null, "assigned_by" integer not null, "created_at" datetime, "updated_at" datetime, foreign key("business_id") references "businesses"("id") on delete cascade, foreign key("tag_id") references "tags"("id") on delete cascade, foreign key("assigned_by") references "users"("id") on delete cascade);

-- Table: business_products
CREATE TABLE "business_products" ("id" integer primary key autoincrement not null, "business_id" integer not null, "product_id" integer not null, "status" varchar check ("status" in ('active', 'inactive', 'pending', 'cancelled')) not null default 'active', "start_date" date, "end_date" date, "notes" text, "custom_features" text, "assigned_by" integer not null, "created_at" datetime, "updated_at" datetime, "implementation_date" date, "contract_value" numeric, "renewal_date" date, "product_version" varchar, foreign key("business_id") references "businesses"("id") on delete cascade, foreign key("product_id") references "products"("id") on delete cascade, foreign key("assigned_by") references "users"("id") on delete cascade);

-- Table: business_comments
CREATE TABLE "business_comments" ("id" integer primary key autoincrement not null, "business_id" integer not null, "user_id" integer not null, "activity_id" integer, "content" text not null, "content_html" text, "mentions" text, "is_edited" tinyint(1) not null default '0', "edited_at" datetime, "parent_id" integer, "is_visible" tinyint(1) not null default '1', "created_at" datetime, "updated_at" datetime, foreign key("business_id") references "businesses"("id") on delete cascade, foreign key("user_id") references "users"("id") on delete cascade, foreign key("activity_id") references "business_activities"("id") on delete cascade, foreign key("parent_id") references "business_comments"("id") on delete cascade);

-- Table: business_notification_preferences
CREATE TABLE "business_notification_preferences" ("id" integer primary key autoincrement not null, "user_id" integer not null, "business_id" integer not null, "email_enabled" tinyint(1) not null default '1', "sms_enabled" tinyint(1) not null default '0', "whatsapp_enabled" tinyint(1) not null default '0', "in_app_enabled" tinyint(1) not null default '1', "frequency" varchar check ("frequency" in ('immediate', 'hourly', 'daily', 'weekly', 'never')) not null default 'immediate', "activity_types" text, "min_severity" varchar check ("min_severity" in ('info', 'warning', 'important', 'critical')) not null default 'info', "notification_email" varchar, "notification_phone" varchar, "notification_whatsapp" varchar, "quiet_hours_start" time, "quiet_hours_end" time, "quiet_days" text, "last_notification_sent" datetime, "created_at" datetime, "updated_at" datetime, foreign key("user_id") references "users"("id") on delete cascade, foreign key("business_id") references "businesses"("id") on delete cascade);

-- Table: business_activity_attachments
CREATE TABLE "business_activity_attachments" ("id" integer primary key autoincrement not null, "activity_id" integer not null, "business_id" integer not null, "uploaded_by" integer not null, "filename" varchar not null, "original_filename" varchar not null, "file_path" varchar not null, "file_type" varchar not null, "file_extension" varchar not null, "file_size" integer not null, "attachment_type" varchar check ("attachment_type" in ('document', 'image', 'video', 'audio', 'archive', 'other')) not null default 'document', "thumbnail_path" varchar, "has_preview" tinyint(1) not null default '0', "metadata" text, "is_public" tinyint(1) not null default '0', "allowed_users" text, "download_count" integer not null default '0', "last_downloaded_at" datetime, "created_at" datetime, "updated_at" datetime, foreign key("activity_id") references "business_activities"("id") on delete cascade, foreign key("business_id") references "businesses"("id") on delete cascade, foreign key("uploaded_by") references "users"("id") on delete cascade);

-- Table: business_activities
CREATE TABLE "business_activities" ("id" integer primary key autoincrement not null, "business_id" integer not null, "user_id" integer, "type" varchar not null, "category" varchar not null default ('user_action'), "severity" varchar not null default ('info'), "title" varchar not null, "description" text, "metadata" text, "subject_type" varchar, "subject_id" integer, "is_visible" tinyint(1) not null default ('1'), "is_system_generated" tinyint(1) not null default ('0'), "created_at" datetime, "updated_at" datetime, "edited_at" datetime, "reply_to_id" integer, foreign key("user_id") references users("id") on delete set null on update no action, foreign key("business_id") references businesses("id") on delete cascade on update no action, foreign key("reply_to_id") references "business_activities"("id") on delete set null);

-- Table: products
CREATE TABLE "products" ("id" integer primary key autoincrement not null, "name" varchar not null, "slug" varchar not null, "description" text, "icon" varchar, "target_audience" text, "use_cases" text, "reference_links" text, "is_active" tinyint(1) not null default '1', "created_by" integer not null, "created_at" datetime, "updated_at" datetime, "terms_and_conditions" text, "service_level_agreement" text, "privacy_policy" text, "usage_policy" text, "quotation_notes" text, "implementation_notes" text, "default_contract_duration_months" integer, "default_discount_percentage" numeric, "support_details" text, "warranty_details" text, "maintenance_details" text, foreign key("created_by") references "users"("id") on delete cascade);

-- Data for table: products
INSERT INTO "products" VALUES ('16', 'whatsapp business api', 'whatsapp-business-api', NULL, 'fab fa-whatsapp', NULL, NULL, '[\"https:\\/\\/taqnyat.sa\\/ar\\/channels\\/WhatsApp-Business-API-service-provider\\/\",\"https:\\/\\/developers.facebook.com\\/docs\\/whatsapp\\/pricing\\/\"]', '1', '1', '2025-07-30 21:34:16', '2025-07-30 21:39:28', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

-- Table: product_documents
CREATE TABLE "product_documents" ("id" integer primary key autoincrement not null, "product_id" integer not null, "category" varchar check ("category" in ('manual', 'brochure', 'policies', 'other')) not null default 'other', "name" varchar not null, "description" text, "file_name" varchar not null, "original_name" varchar not null, "file_path" varchar not null, "mime_type" varchar not null, "file_size" integer not null, "version" varchar not null default '1.0', "is_current_version" tinyint(1) not null default '1', "uploaded_by" integer not null, "upload_date" datetime not null, "created_at" datetime, "updated_at" datetime, foreign key("product_id") references "products"("id") on delete cascade, foreign key("uploaded_by") references "users"("id") on delete cascade);

-- Table: product_releases
CREATE TABLE "product_releases" ("id" integer primary key autoincrement not null, "product_id" integer not null, "version" varchar not null, "title" varchar not null, "description" text, "changelog" text, "release_date" date not null, "is_published" tinyint(1) not null default '0', "release_type" varchar check ("release_type" in ('major', 'minor', 'patch', 'hotfix')) not null default 'minor', "features" text, "bug_fixes" text, "breaking_changes" text, "created_by" integer not null, "published_at" datetime, "created_at" datetime, "updated_at" datetime, foreign key("product_id") references "products"("id") on delete cascade, foreign key("created_by") references "users"("id") on delete cascade);

-- Table: product_pricing_items
CREATE TABLE "product_pricing_items" ("id" integer primary key autoincrement not null, "product_id" integer not null, "name" varchar not null, "description" text, "price" numeric, "currency" varchar not null default 'USD', "billing_cycle" varchar check ("billing_cycle" in ('one-time', 'monthly', 'quarterly', 'yearly', 'per_consumption', 'custom')) not null default 'monthly', "features" text, "is_active" tinyint(1) not null default '1', "sort_order" integer not null default '0', "is_popular" tinyint(1) not null default '0', "button_text" varchar, "notes" text, "created_at" datetime, "updated_at" datetime, "pricing_model" varchar check ("pricing_model" in ('fixed', 'per_unit', 'tiered', 'usage_based', 'custom')) not null default 'fixed', "unit_type" varchar, "unit_label" varchar, "min_quantity" integer, "max_quantity" integer, "setup_fee" numeric, "category" varchar, "metadata" text, "include_in_quotations" tinyint(1) not null default '1', "quotation_description" text, foreign key("product_id") references "products"("id") on delete cascade);

-- Data for table: product_pricing_items
INSERT INTO "product_pricing_items" VALUES ('44', '16', 'Marketing', NULL, '0.18', 'SAR', 'per_consumption', NULL, '1', '0', '0', NULL, NULL, '2025-07-30 21:34:46', '2025-07-30 21:34:46', 'per_unit', 'message', NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL);
INSERT INTO "product_pricing_items" VALUES ('45', '16', 'Utility', NULL, '0.05', 'SAR', 'per_consumption', NULL, '1', '0', '0', NULL, NULL, '2025-07-30 21:35:07', '2025-07-30 21:35:07', 'per_unit', 'message', NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL);
INSERT INTO "product_pricing_items" VALUES ('46', '16', 'Authintication', NULL, '0.05', 'SAR', 'per_consumption', NULL, '1', '0', '0', NULL, NULL, '2025-07-30 21:35:27', '2025-07-30 21:35:27', 'per_unit', 'message', NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL);
INSERT INTO "product_pricing_items" VALUES ('47', '16', 'Subscription', NULL, '600', 'SAR', 'monthly', NULL, '1', '0', '0', NULL, NULL, '2025-07-30 21:35:49', '2025-07-30 21:37:33', 'fixed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL);
INSERT INTO "product_pricing_items" VALUES ('48', '16', 'Setup Fee', NULL, '0', 'SAR', 'one-time', NULL, '1', '0', '0', NULL, NULL, '2025-07-30 21:37:51', '2025-07-30 21:38:21', 'fixed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL);

-- Table: announcements
CREATE TABLE "announcements" ("id" integer primary key autoincrement not null, "title" varchar not null, "content" text not null, "priority" varchar check ("priority" in ('low', 'normal', 'high', 'urgent')) not null default 'normal', "type" varchar check ("type" in ('info', 'warning', 'success', 'error')) not null default 'info', "is_active" tinyint(1) not null default '1', "requires_acknowledgment" tinyint(1) not null default '1', "published_at" datetime, "expires_at" datetime, "target_roles" text, "metadata" text, "created_by" integer not null, "created_at" datetime, "updated_at" datetime, "deleted_at" datetime, foreign key("created_by") references "users"("id") on delete cascade);

-- Data for table: announcements
INSERT INTO "announcements" VALUES ('51', 'اضافة التوقيع', 'اضافة التوقيع في جميع الايميلات المرسلة والتي يتم الرد عليها', 'normal', 'info', '1', '1', '2025-07-30 12:46:00', NULL, '[\"admin\",\"editor\",\"user\"]', NULL, '1', '2025-07-30 12:47:52', '2025-07-30 12:47:52', NULL);
INSERT INTO "announcements" VALUES ('52', 'اضافة ايميل الادارة', 'اضافة ايميل الادارة الى جميع الايميلات المرسلة داخليا وخارجيا 
الايميل الخاص بالادارة هو
<EMAIL>', 'high', 'warning', '1', '1', '2025-07-30 12:47:00', NULL, '[\"admin\",\"editor\",\"user\"]', NULL, '1', '2025-07-30 12:48:59', '2025-07-30 12:48:59', NULL);
INSERT INTO "announcements" VALUES ('53', 'test', 'asdasdasdasd', 'low', 'info', '1', '1', '2025-08-01 15:41:00', NULL, NULL, NULL, '1', '2025-08-01 15:42:08', '2025-08-01 15:42:21', '2025-08-01 15:42:21');

-- Table: announcement_reads
CREATE TABLE "announcement_reads" ("id" integer primary key autoincrement not null, "announcement_id" integer not null, "user_id" integer not null, "read_at" datetime, "acknowledged_at" datetime, "ip_address" varchar, "user_agent" varchar, "metadata" text, "created_at" datetime, "updated_at" datetime, foreign key("announcement_id") references "announcements"("id") on delete cascade, foreign key("user_id") references "users"("id") on delete cascade);

-- Data for table: announcement_reads
INSERT INTO "announcement_reads" VALUES ('78', '52', '1', '2025-07-30 12:50:23', NULL, '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '[]', '2025-07-30 12:50:23', '2025-07-30 12:50:23');
INSERT INTO "announcement_reads" VALUES ('79', '51', '1', '2025-07-30 12:50:23', NULL, '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '[]', '2025-07-30 12:50:23', '2025-07-30 12:50:23');
INSERT INTO "announcement_reads" VALUES ('80', '53', '1', '2025-08-01 15:42:14', '2025-08-01 15:42:14', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '[]', '2025-08-01 15:42:14', '2025-08-01 15:42:14');
INSERT INTO "announcement_reads" VALUES ('81', '51', '3', '2025-08-01 15:42:37', NULL, '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '[]', '2025-08-01 15:42:37', '2025-08-01 15:42:37');
INSERT INTO "announcement_reads" VALUES ('82', '52', '3', '2025-08-01 15:42:37', NULL, '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '[]', '2025-08-01 15:42:37', '2025-08-01 15:42:37');
INSERT INTO "announcement_reads" VALUES ('83', '51', '2', '2025-08-01 16:54:08', NULL, '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '[]', '2025-08-01 16:54:08', '2025-08-01 16:54:08');
INSERT INTO "announcement_reads" VALUES ('84', '52', '2', '2025-08-01 16:54:08', NULL, '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '[]', '2025-08-01 16:54:08', '2025-08-01 16:54:08');

-- Table: languages
CREATE TABLE "languages" ("id" integer primary key autoincrement not null, "code" varchar not null, "name" varchar not null, "native_name" varchar not null, "flag_icon" varchar, "direction" varchar check ("direction" in ('ltr', 'rtl')) not null default 'ltr', "is_active" tinyint(1) not null default '1', "is_default" tinyint(1) not null default '0', "sort_order" integer not null default '0', "locale" varchar, "country_code" varchar, "currency_code" varchar, "date_format" varchar not null default 'Y-m-d', "time_format" varchar not null default 'H:i', "number_format" varchar not null default '1,234.56', "translation_count" integer not null default '0', "completion_percentage" numeric not null default '0', "metadata" text, "created_at" datetime, "updated_at" datetime);

-- Data for table: languages
INSERT INTO "languages" VALUES ('1', 'en', 'English', 'English', '🇺🇸', 'ltr', '1', '1', '1', 'en_US', 'US', 'USD', 'Y-m-d', 'H:i', '1,234.56', '0', '0', NULL, '2025-07-30 13:50:29', '2025-07-30 13:50:29');
INSERT INTO "languages" VALUES ('2', 'es', 'Spanish', 'Español', '🇪🇸', 'ltr', '1', '0', '2', 'es_ES', 'ES', 'EUR', 'd/m/Y', 'H:i', '1.234,56', '0', '0', NULL, '2025-07-30 13:50:29', '2025-07-30 13:50:29');
INSERT INTO "languages" VALUES ('3', 'fr', 'French', 'Français', '🇫🇷', 'ltr', '1', '0', '3', 'fr_FR', 'FR', 'EUR', 'd/m/Y', 'H:i', '1 234,56', '0', '0', NULL, '2025-07-30 13:50:29', '2025-07-30 13:50:29');
INSERT INTO "languages" VALUES ('4', 'ar', 'Arabic', 'العربية', '🇸🇦', 'rtl', '1', '0', '4', 'ar_SA', 'SA', 'SAR', 'd/m/Y', 'H:i', '1,234.56', '0', '0', NULL, '2025-07-30 13:50:29', '2025-07-30 13:50:29');

-- Table: taqnyat_settings
CREATE TABLE "taqnyat_settings" ("id" integer primary key autoincrement not null, "key" varchar not null, "value" text, "type" varchar not null default 'string', "description" text, "created_at" datetime, "updated_at" datetime);

-- Data for table: taqnyat_settings
INSERT INTO "taqnyat_settings" VALUES ('1', 'api_timeout', '30', 'integer', 'API request timeout in seconds', '2025-07-31 14:19:12', '2025-07-31 14:19:12');
INSERT INTO "taqnyat_settings" VALUES ('2', 'session_timeout', '60', 'integer', 'Session timeout in minutes', '2025-07-31 14:19:12', '2025-07-31 14:19:12');
INSERT INTO "taqnyat_settings" VALUES ('3', 'auto_refresh', 'false', 'boolean', 'Enable automatic session refresh', '2025-07-31 14:19:12', '2025-07-31 14:19:12');
INSERT INTO "taqnyat_settings" VALUES ('4', 'debug_logging', 'false', 'boolean', 'Enable debug logging for API requests', '2025-07-31 14:19:12', '2025-07-31 14:19:12');
INSERT INTO "taqnyat_settings" VALUES ('5', 'base_url', 'https://cp.taqnyat.sa', 'string', 'Taqnyat API base URL', '2025-07-31 14:19:12', '2025-07-31 14:19:12');

-- Table: user_preferences
CREATE TABLE "user_preferences" ("id" integer primary key autoincrement not null, "user_id" integer not null, "theme" varchar check ("theme" in ('light', 'dark', 'system')) not null default 'light', "language" varchar not null default 'en', "sidebar_collapsed" tinyint(1) not null default '0', "sidebar_position" varchar check ("sidebar_position" in ('left', 'right')) not null default 'left', "email_notifications" tinyint(1) not null default '1', "browser_notifications" tinyint(1) not null default '1', "sound_notifications" tinyint(1) not null default '0', "date_format" varchar check ("date_format" in ('Y-m-d', 'm/d/Y', 'd/m/Y', 'd-m-Y')) not null default 'Y-m-d', "time_format" varchar check ("time_format" in ('24', '12')) not null default '24', "timezone" varchar not null default 'UTC', "additional_preferences" text, "created_at" datetime, "updated_at" datetime, foreign key("user_id") references "users"("id") on delete cascade);

-- Data for table: user_preferences
INSERT INTO "user_preferences" VALUES ('1', '1', 'light', 'en', '0', 'left', '1', '1', '0', 'Y-m-d', '24', 'UTC', '[]', '2025-08-01 15:40:12', '2025-08-01 15:40:12');
INSERT INTO "user_preferences" VALUES ('2', '3', 'light', 'en', '0', 'left', '1', '1', '0', 'Y-m-d', '24', 'UTC', '[]', '2025-08-01 15:42:32', '2025-08-01 15:42:32');
INSERT INTO "user_preferences" VALUES ('3', '2', 'light', 'en', '0', 'left', '1', '1', '0', 'Y-m-d', '24', 'UTC', '[]', '2025-08-01 16:54:04', '2025-08-01 16:54:04');

-- Table: navigation_menus
CREATE TABLE "navigation_menus" ("id" integer primary key autoincrement not null, "name" varchar not null, "label" varchar not null, "icon" varchar, "url" varchar, "route" varchar, "plugin" varchar, "permissions" text, "parent_id" integer, "sort_order" integer not null default '0', "is_active" tinyint(1) not null default '1', "is_system" tinyint(1) not null default '0', "target" varchar not null default '_self', "metadata" text, "created_at" datetime, "updated_at" datetime, foreign key("parent_id") references "navigation_menus"("id") on delete cascade);

-- Data for table: navigation_menus
INSERT INTO "navigation_menus" VALUES ('1', 'dashboard', 'Dashboard', 'fas fa-tachometer-alt', NULL, 'dashboard', NULL, '[\"view_dashboard\"]', NULL, '1', '1', '1', '_self', NULL, '2025-08-01 17:18:13', '2025-08-01 17:18:13');
INSERT INTO "navigation_menus" VALUES ('2', 'announcements', 'Announcements', 'fas fa-bullhorn', NULL, 'announcements.index', 'announcements', '[\"manage_announcements\",\"view_announcements\"]', NULL, '2', '1', '0', '_self', NULL, '2025-08-01 17:18:13', '2025-08-01 17:18:13');
INSERT INTO "navigation_menus" VALUES ('3', 'business', 'Business', 'fas fa-building', NULL, NULL, 'business', '[\"manage_businesses\",\"view_businesses\"]', NULL, '3', '1', '0', '_self', NULL, '2025-08-01 17:18:13', '2025-08-01 17:18:13');
INSERT INTO "navigation_menus" VALUES ('4', 'businesses-list', 'All Businesses', 'fas fa-building', NULL, 'business.index', 'business', '[\"manage_businesses\",\"view_businesses\"]', '3', '1', '1', '0', '_self', NULL, '2025-08-01 17:18:13', '2025-08-01 17:18:13');
INSERT INTO "navigation_menus" VALUES ('5', 'contacts-list', 'Contacts', 'fas fa-address-book', NULL, 'business.contacts.index', 'business', '[\"manage_contacts\",\"view_contacts\"]', '3', '2', '1', '0', '_self', NULL, '2025-08-01 17:18:13', '2025-08-01 17:18:13');
INSERT INTO "navigation_menus" VALUES ('6', 'settings', 'Settings', 'fas fa-cog', NULL, 'settings.index', 'settings', '[\"manage_settings\"]', NULL, '4', '1', '0', '_self', NULL, '2025-08-01 17:18:13', '2025-08-01 17:18:13');
INSERT INTO "navigation_menus" VALUES ('7', 'users', 'Users', 'fas fa-users', NULL, NULL, 'users', '[\"manage_users\",\"manage_roles\",\"manage_permissions\"]', NULL, '5', '1', '0', '_self', NULL, '2025-08-01 17:18:13', '2025-08-01 17:18:13');
INSERT INTO "navigation_menus" VALUES ('8', 'users-list', 'All Users', 'fas fa-user', NULL, 'users.index', 'users', '[\"manage_users\"]', '7', '1', '1', '0', '_self', NULL, '2025-08-01 17:18:13', '2025-08-01 17:18:13');
INSERT INTO "navigation_menus" VALUES ('9', 'roles-list', 'Roles', 'fas fa-user-tag', NULL, 'roles.index', 'users', '[\"manage_roles\"]', '7', '2', '1', '0', '_self', NULL, '2025-08-01 17:18:13', '2025-08-01 17:18:13');
INSERT INTO "navigation_menus" VALUES ('10', 'permissions-list', 'Permissions', 'fas fa-key', NULL, 'permissions.index', 'users', '[\"manage_permissions\"]', '7', '3', '1', '0', '_self', NULL, '2025-08-01 17:18:13', '2025-08-01 17:18:13');
INSERT INTO "navigation_menus" VALUES ('11', 'navigation', 'Navigation', 'fas fa-sitemap', NULL, 'navigation.index', NULL, '[\"manage_plugins\"]', NULL, '6', '1', '1', '_self', NULL, '2025-08-01 17:18:13', '2025-08-01 17:18:13');
INSERT INTO "navigation_menus" VALUES ('12', 'plugins', 'Plugins', 'fas fa-puzzle-piece', NULL, 'plugins.index', NULL, '[\"manage_plugins\"]', NULL, '7', '1', '1', '_self', NULL, '2025-08-01 17:18:13', '2025-08-01 17:18:13');
INSERT INTO "navigation_menus" VALUES ('13', 'example', 'example', 'fas fa-circle', 'https://www.taqnyat.sa', NULL, NULL, '[]', NULL, '8', '0', '0', '_self', NULL, '2025-08-01 17:32:52', '2025-08-01 17:33:11');
INSERT INTO "navigation_menus" VALUES ('14', 'taqnyat', 'Taqnyat', 'fas fa-circle', 'https://www.taqnyat.sa', 'taqnyat', NULL, '[]', NULL, '9', '1', '0', '_blank', NULL, '2025-08-01 17:34:12', '2025-08-01 17:36:42');

