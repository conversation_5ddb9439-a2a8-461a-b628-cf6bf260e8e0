<?php

/**
 * Safe backup restoration script that handles migration ID conflicts
 */

$dbPath = __DIR__ . '/database/database.sqlite';
$backupPath = __DIR__ . '/storage/app/backups/backup-2025-08-01_17-43-42/database.sql';

echo "=== Safe Backup Restoration ===\n";

if (!file_exists($backupPath)) {
    die("❌ Backup file not found: $backupPath\n");
}

try {
    // Read backup file
    echo "📖 Reading backup file...\n";
    $backupSql = file_get_contents($backupPath);
    
    // Remove existing database
    if (file_exists($dbPath)) {
        echo "🗑️  Removing existing database...\n";
        unlink($dbPath);
    }
    
    // Create new empty database
    echo "🆕 Creating new database...\n";
    touch($dbPath);
    
    // Connect to database
    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->exec('PRAGMA foreign_keys = ON');
    
    // Process the backup SQL
    echo "⚙️  Processing backup SQL...\n";
    
    // Split into statements
    $statements = preg_split('/;\s*\n/', $backupSql);
    
    $processed = 0;
    $errors = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        
        // Skip empty statements and comments
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        // Add semicolon if missing
        if (!empty($statement) && substr($statement, -1) !== ';') {
            $statement .= ';';
        }
        
        try {
            $pdo->exec($statement);
            $processed++;
            
            if ($processed % 50 === 0) {
                echo "   Processed $processed statements...\n";
            }
        } catch (PDOException $e) {
            $errors++;
            if ($errors <= 5) { // Only show first 5 errors
                echo "⚠️  Warning: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "\n✅ Restoration completed!\n";
    echo "📊 Statistics:\n";
    echo "   - Processed: $processed statements\n";
    echo "   - Errors: $errors statements\n";
    
    // Verify restoration
    echo "\n🔍 Verifying restoration...\n";
    
    $tables = ['migrations', 'users', 'businesses', 'products'];
    foreach ($tables as $table) {
        try {
            $result = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $result->fetch(PDO::FETCH_ASSOC)['count'];
            echo "   - $table: $count records\n";
        } catch (PDOException $e) {
            echo "   - $table: Table not found or error\n";
        }
    }
    
    echo "\n🎉 Backup restoration completed successfully!\n";
    echo "Your application should now work with the restored data.\n";
    
} catch (Exception $e) {
    echo "❌ Fatal error: " . $e->getMessage() . "\n";
    exit(1);
}
